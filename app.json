{"expo": {"name": "GoGo", "slug": "gogo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "gogo", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.helldog.GoGo"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4032cb55-2847-42a0-98e7-2927a68f0c1d"}}, "owner": "maria2344"}}