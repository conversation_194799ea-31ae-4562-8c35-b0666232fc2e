import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Share,
  Platform,
  AccessibilityInfo,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Video } from '../types';
import { GoGoColors } from '../../constants/Colors';
import { formatRelativeTime, formatViewCount } from '../utils/formatters';

const { width } = Dimensions.get('window');

interface VideoCardProps {
  video: Video;
  onPress: (video: Video) => void;
  size?: 'small' | 'medium' | 'large';
  progress?: number;
  isWatched?: boolean;
  onAddToList?: () => void;
  onShare?: () => void;
  onRate?: (rating: 'up' | 'down') => void;
  onPinch?: (scale: number) => void;
}

export default function VideoCard({
  video,
  onPress,
  size = 'medium',
  progress = 0,
  isWatched = false,
  onAddToList,
  onShare,
  onRate,
  onPinch,
}: VideoCardProps) {
  const cardWidth = size === 'large' ? width * 0.8 : size === 'medium' ? width * 0.4 : width * 0.3;
  const cardHeight = cardWidth * 1.5;
  const scale = useSharedValue(1);
  const [showPreview, setShowPreview] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const previewTimeout = useRef<NodeJS.Timeout>();
  const lastTap = useRef<number>(0);
  const [isAccessibilityEnabled, setIsAccessibilityEnabled] = useState(false);

  useEffect(() => {
    const checkAccessibility = async () => {
      const isEnabled = await AccessibilityInfo.isScreenReaderEnabled();
      setIsAccessibilityEnabled(isEnabled);
    };
    checkAccessibility();
  }, []);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    previewTimeout.current = setTimeout(() => {
      setShowPreview(true);
    }, 500) as unknown as NodeJS.Timeout;
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
    if (previewTimeout.current) {
      clearTimeout(previewTimeout.current);
    }
    setShowPreview(false);
  };

  const handleDoubleTap = () => {
    const now = Date.now();
    if (now - lastTap.current < 300) {
      setIsLiked(true);
      setIsDisliked(false);
      onRate?.('up');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    lastTap.current = now;
  };

  const handleLongPress = () => {
    setShowQuickActions(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out ${video.title} on StreamFlix!`,
        url: video.thumbnail_url,
      });
      onShare?.();
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleAddToList = () => {
    onAddToList?.();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleRate = (rating: 'up' | 'down') => {
    if (rating === 'up') {
      setIsLiked(!isLiked);
      setIsDisliked(false);
    } else {
      setIsDisliked(!isDisliked);
      setIsLiked(false);
    }
    onRate?.(rating);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  return (
    <Animated.View style={[styles.videoCardContainer, { width: cardWidth, borderRadius: 16, shadowColor: '#000', shadowOpacity: 0.15, shadowRadius: 8, elevation: 6 }]}>
      <TouchableOpacity
        style={[styles.videoCard, { height: cardHeight, borderRadius: 16, overflow: 'hidden' }]}
        onPress={() => onPress(video)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onLongPress={handleLongPress}
        onPress={handleDoubleTap}
        activeOpacity={0.95}
        accessible={isAccessibilityEnabled}
        accessibilityLabel={`${video.title}, by ${video.creator.username}`}
        accessibilityHint="Double tap to play, long press for more options"
      >
        <View style={{ flex: 1 }}>
          <Image
            source={{ uri: video.thumbnail_url }}
            style={styles.videoThumbnailModern}
            resizeMode="cover"
          />

          {/* Progress bar for continue watching */}
          {progress > 0 && (
            <View style={styles.progressContainer}>
              <View
                style={[styles.progressBar, { width: `${Math.min(progress, 100)}%` }]}
              />
            </View>
          )}

          <LinearGradient
            colors={["transparent", "rgba(0,0,0,0.85)"]}
            style={styles.gradientOverlay}
          />
          <TouchableOpacity style={styles.playIconModern} accessibilityLabel="Play video">
            <Ionicons name="play-circle" size={48} color="#fff" />
          </TouchableOpacity>
          <View style={styles.creatorRow}>
            <Image
              source={{ uri: video.creator.avatar_url || undefined }}
              style={styles.creatorAvatar}
            />
            <Text style={styles.creatorName}>{video.creator.username}</Text>
            <Text style={styles.dot}>•</Text>
            <Text style={styles.timeAgo}>{formatRelativeTime(video.created_at)}</Text>
          </View>
          <View style={styles.titleRow}>
            <Text style={styles.videoTitleModern} numberOfLines={2}>{video.title}</Text>
            {video.is_premium && (
              <View style={styles.premiumBadgeModern}>
                <Ionicons name="star" size={12} color="#FFD700" style={{ marginRight: 2 }} />
                <Text style={styles.premiumTextModern}>PREMIUM</Text>
              </View>
            )}
          </View>
          <View style={styles.tagsRow}>
            {video.tags.slice(0, 3).map(tag => (
              <View key={tag} style={styles.tagChip}>
                <Text style={styles.tagText}>#{tag}</Text>
              </View>
            ))}
          </View>
        </View>
        <View style={styles.socialRowModern}>
          <TouchableOpacity
            style={styles.socialButtonModern}
            onPress={() => {
              setIsLiked(!isLiked);
            }}
            accessibilityLabel="Like video"
          >
            <Ionicons
              name={isLiked ? 'heart' : 'heart-outline'}
              size={22}
              color={isLiked ? StreamFlixColors.primary : '#fff'}
            />
            <Text style={styles.socialCountModern}>{formatViewCount(video.like_count + (isLiked ? 1 : 0))}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.socialButtonModern}
            onPress={() => alert('Comments feature coming soon!')}
            accessibilityLabel="View comments"
          >
            <Ionicons name="chatbubble-ellipses-outline" size={20} color="#fff" />
            <Text style={styles.socialCountModern}>45</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.socialButtonModern}
            onPress={handleShare}
            accessibilityLabel="Share video"
          >
            <Ionicons name="share-social-outline" size={20} color="#fff" />
          </TouchableOpacity>
          <View style={styles.socialButtonModern} accessibilityLabel="View count">
            <Ionicons name="eye-outline" size={20} color="#fff" />
            <Text style={styles.socialCountModern}>{formatViewCount(video.view_count)}</Text>
          </View>
        </View>
      </TouchableOpacity>

      {showPreview && (
        <Animated.View style={styles.previewContainer}>
          <Image
            source={{ uri: video.thumbnail_url }}
            style={styles.previewImage}
            resizeMode="cover"
          />
          <View style={styles.previewInfo}>
            <Text style={styles.previewTitle}>{video.title}</Text>
            <Text style={styles.previewMeta}>
              {video.view_count} views • {video.creator.username}
            </Text>
            <View style={styles.previewActions}>
              <TouchableOpacity 
                style={styles.previewButton}
                onPress={() => onPress(video)}
              >
                <Ionicons name="play" size={20} color="#FFFFFF" />
                <Text style={styles.previewButtonText}>Play</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.previewButton}
                onPress={handleAddToList}
              >
                <Ionicons name="add" size={20} color="#FFFFFF" />
                <Text style={styles.previewButtonText}>My List</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.previewButton}
                onPress={handleShare}
              >
                <Ionicons name="share-outline" size={20} color="#FFFFFF" />
                <Text style={styles.previewButtonText}>Share</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.ratingContainer}>
              <TouchableOpacity 
                style={[styles.ratingButton, isLiked && styles.ratingButtonActive]}
                onPress={() => handleRate('up')}
              >
                <Ionicons 
                  name="thumbs-up" 
                  size={20} 
                  color={isLiked ? '#FFFFFF' : '#CCCCCC'} 
                />
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.ratingButton, isDisliked && styles.ratingButtonActive]}
                onPress={() => handleRate('down')}
              >
                <Ionicons 
                  name="thumbs-down" 
                  size={20} 
                  color={isDisliked ? '#FFFFFF' : '#CCCCCC'} 
                />
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      )}

      {showQuickActions && (
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity 
            style={styles.quickAction}
            onPress={() => {
              setShowQuickActions(false);
              onPress(video);
            }}
          >
            <Ionicons name="play" size={24} color="#FFFFFF" />
            <Text style={styles.quickActionText}>Play</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.quickAction}
            onPress={() => {
              setShowQuickActions(false);
              handleAddToList();
            }}
          >
            <Ionicons name="add" size={24} color="#FFFFFF" />
            <Text style={styles.quickActionText}>Add to List</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.quickAction}
            onPress={() => {
              setShowQuickActions(false);
              handleShare();
            }}
          >
            <Ionicons name="share-outline" size={24} color="#FFFFFF" />
            <Text style={styles.quickActionText}>Share</Text>
          </TouchableOpacity>
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  videoCardContainer: {
    marginRight: 10,
  },
  videoCard: {
    borderRadius: 4,
    overflow: 'hidden',
    backgroundColor: GoGoColors.backgroundCard,
    position: 'relative',
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    zIndex: 1,
  },
  progressBar: {
    height: '100%',
    backgroundColor: GoGoColors.primary,
    borderRadius: 2,
  },
  videoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40%',
    justifyContent: 'flex-end',
  },
  videoInfo: {
    padding: 10,
  },
  videoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  premiumBadge: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 2,
    alignSelf: 'flex-start',
  },
  premiumText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  playIconContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    opacity: 0,
  },
  previewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 8,
    overflow: 'hidden',
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  previewImage: {
    width: '100%',
    height: 200,
  },
  previewInfo: {
    padding: 15,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  previewMeta: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 15,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 15,
  },
  previewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 4,
    gap: 8,
  },
  previewButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  ratingContainer: {
    flexDirection: 'row',
    gap: 15,
  },
  ratingButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  ratingButtonActive: {
    backgroundColor: StreamFlixColors.primary,
  },
  quickActionsContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -100 }, { translateY: -50 }],
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: 8,
    padding: 15,
    zIndex: 1001,
  },
  quickAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    gap: 10,
  },
  quickActionText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  socialRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 4,
    paddingHorizontal: 8,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
  },
  socialCount: {
    color: '#fff',
    fontSize: 14,
    marginLeft: 4,
  },
  videoThumbnailModern: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  gradientOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '55%',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  playIconModern: {
    position: 'absolute',
    bottom: 60,
    left: '50%',
    transform: [{ translateX: -24 }],
    zIndex: 2,
  },
  creatorRow: {
    position: 'absolute',
    left: 10,
    bottom: 70,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 2,
  },
  creatorAvatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginRight: 8,
    backgroundColor: StreamFlixColors.glassBg,
  },
  creatorName: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 13,
  },
  dot: {
    color: '#fff',
    marginHorizontal: 4,
    fontSize: 13,
  },
  timeAgo: {
    color: '#ccc',
    fontSize: 12,
  },
  titleRow: {
    position: 'absolute',
    left: 10,
    bottom: 40,
    right: 10,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 2,
  },
  videoTitleModern: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    flex: 1,
  },
  premiumBadgeModern: {
    backgroundColor: 'rgba(255,215,0,0.15)',
    borderColor: '#FFD700',
    borderWidth: 1,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  premiumTextModern: {
    color: '#FFD700',
    fontWeight: 'bold',
    fontSize: 11,
  },
  tagsRow: {
    position: 'absolute',
    left: 10,
    bottom: 18,
    flexDirection: 'row',
    zIndex: 2,
  },
  tagChip: {
    backgroundColor: 'rgba(255,255,255,0.12)',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 6,
  },
  tagText: {
    color: '#fff',
    fontSize: 11,
  },
  socialRowModern: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginTop: -8,
    zIndex: 3,
  },
  socialButtonModern: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
    minWidth: 44,
    justifyContent: 'center',
  },
  socialCountModern: {
    color: '#fff',
    fontSize: 14,
    marginLeft: 4,
  },
});
