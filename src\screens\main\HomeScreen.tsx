import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  FlatList,
  ActivityIndicator,
  Pressable,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Video } from 'expo-av';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchVideos, fetchCategories, fetchRecommendations } from '../../store/slices/videoSlice';
import { Video as VideoType, Category } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import VideoCard from '../../components/VideoCard';
import { useResponsiveLayout } from '../../utils/responsive';
import { hapticFeedback } from '../../utils/animations';
import { formatViewCount, formatDuration } from '../../utils/formatters';

const { width } = Dimensions.get('window');

interface VideoCardProps {
  video: VideoType;
  onPress: (video: VideoType) => void;
  size?: 'small' | 'medium' | 'large';
}

// Loading Skeleton Component
function VideoCardSkeleton({ size = 'medium' }: { size?: 'small' | 'medium' | 'large' }) {
  const cardWidth = size === 'large' ? width * 0.8 : size === 'medium' ? width * 0.4 : width * 0.3;
  const cardHeight = cardWidth * 1.5;
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withTiming(1, { 
      duration: 1000,
      repeat: -1,
      repeatMode: 'reverse'
    });
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateX: interpolate(
            shimmerValue.value,
            [0, 1],
            [-cardWidth, cardWidth],
            Extrapolate.CLAMP
          ),
        },
      ],
    };
  });

  return (
    <View style={[styles.videoCardContainer, { width: cardWidth }]}>
      <View style={[styles.videoCard, { height: cardHeight }]}>
        <View style={[styles.skeletonContainer, { height: cardHeight }]}>
          <Animated.View style={[styles.shimmer, shimmerStyle]} />
        </View>
      </View>
    </View>
  );
}

interface VideoRowProps {
  title: string;
  videos: VideoType[];
  onVideoPress: (video: VideoType) => void;
  size?: 'small' | 'medium' | 'large';
}

// Enhanced Video Row with Scroll Animation
function VideoRow({ title, videos, onVideoPress, size = 'medium' }: VideoRowProps) {
  const scrollX = useSharedValue(0);
  const flatListRef = useRef<FlatList>(null);

  const handleScroll = (event: any) => {
    scrollX.value = event.nativeEvent.contentOffset.x;
  };

  const scrollToNext = () => {
    if (flatListRef.current) {
      const currentIndex = Math.floor(scrollX.value / (width * 0.4));
      flatListRef.current.scrollToIndex({
        index: Math.min(currentIndex + 1, videos.length - 1),
        animated: true,
      });
    }
  };

  return (
    <View style={styles.videoRow}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity>
          <Text style={styles.seeAllButton}>See All</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        ref={flatListRef}
        data={videos}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.videoList}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        renderItem={({ item }) => (
          <VideoCard video={item} onPress={onVideoPress} size={size} />
        )}
      />
      <TouchableOpacity style={styles.scrollButton} onPress={scrollToNext}>
        <Ionicons name="chevron-forward" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );
}

interface Props {
  onVideoPress: (video: VideoType) => void;
}

export default function HomeScreen({ onVideoPress }: Props) {
  const dispatch = useAppDispatch();
  const { videos, categories, recommendations, isLoading } = useAppSelector((state) => state.video);
  const { user } = useAppSelector((state) => state.auth);
  const layout = useResponsiveLayout();

  const [featuredVideo, setFeaturedVideo] = useState<VideoType | null>(null);
  const [isLoadingFeatured, setIsLoadingFeatured] = useState(true);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [showVideoInfo, setShowVideoInfo] = useState(false);
  const [isInMyList, setIsInMyList] = useState(false);
  const videoRef = useRef<Video>(null);

  useEffect(() => {
    // Load initial data
    const loadData = async () => {
      setIsLoadingFeatured(true);
      await Promise.all([
        dispatch(fetchVideos({ page: 1, limit: 20 })),
        dispatch(fetchCategories()),
        user && dispatch(fetchRecommendations(user.id))
      ]);
      setIsLoadingFeatured(false);
    };
    
    loadData();
  }, [dispatch, user]);

  useEffect(() => {
    // Set featured video (first video with high view count)
    if (videos.length > 0) {
      const featured = videos.find(v => v.view_count > 1000) || videos[0];
      setFeaturedVideo(featured);
    }
  }, [videos]);

  const getVideosByCategory = (categoryId: string) => {
    return videos.filter(video => video.category_id === categoryId);
  };

  const getTrendingVideos = () => {
    return [...videos].sort((a, b) => b.view_count - a.view_count).slice(0, 10);
  };

  const getRecentVideos = () => {
    return [...videos].sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    ).slice(0, 10);
  };

  const renderLoadingSkeletons = () => {
    return (
      <>
        <View style={styles.videoRow}>
          <View style={styles.sectionHeader}>
            <View style={styles.skeletonTitle} />
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.videoList}>
            {[1, 2, 3, 4].map((_, index) => (
              <VideoCardSkeleton key={index} size="medium" />
            ))}
          </ScrollView>
        </View>
        <View style={styles.videoRow}>
          <View style={styles.sectionHeader}>
            <View style={styles.skeletonTitle} />
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.videoList}>
            {[1, 2, 3, 4].map((_, index) => (
              <VideoCardSkeleton key={index} size="small" />
            ))}
          </ScrollView>
        </View>
      </>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Enhanced Featured Video Section */}
      {isLoadingFeatured ? (
        <View style={[styles.featuredSection, styles.skeletonFeatured]}>
          <View style={styles.skeletonContainer}>
            <Animated.View style={[styles.shimmer, { height: '100%' }]} />
          </View>
        </View>
      ) : featuredVideo && (
        <View style={styles.featuredSection}>
          {isPreviewPlaying ? (
            <Video
              ref={videoRef}
              source={{ uri: featuredVideo.video_url }}
              style={styles.featuredVideo}
              shouldPlay
              isLooping
              isMuted
              resizeMode="cover"
            />
          ) : (
            <Image
              source={{ uri: featuredVideo.thumbnail_url }}
              style={styles.featuredImage}
              resizeMode="cover"
            />
          )}

          {/* Maturity Rating Badge */}
          <View style={styles.maturityBadge}>
            <Text style={styles.maturityText}>TV-MA</Text>
          </View>

          {/* Premium Badge */}
          {featuredVideo.is_premium && (
            <View style={styles.featuredPremiumBadge}>
              <Ionicons name="diamond" size={16} color="#FFFFFF" />
              <Text style={styles.featuredPremiumText}>PREMIUM</Text>
            </View>
          )}

          <LinearGradient
            colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
            style={styles.featuredOverlay}
          >
            <View style={styles.featuredContent}>
              <Text style={styles.featuredTitle}>{featuredVideo.title}</Text>
              <Text style={styles.featuredDescription} numberOfLines={layout.isTablet ? 4 : 3}>
                {featuredVideo.description}
              </Text>

              <View style={styles.featuredMeta}>
                <View style={styles.ratingContainer}>
                  <Ionicons name="star" size={16} color={GoGoColors.ratingGold} />
                  <Text style={styles.ratingText}>4.5</Text>
                </View>
                <Text style={styles.featuredMetaText}>•</Text>
                <Text style={styles.featuredMetaText}>{formatViewCount(featuredVideo.view_count)} views</Text>
                <Text style={styles.featuredMetaText}>•</Text>
                <Text style={styles.featuredMetaText}>{formatDuration(featuredVideo.duration)}</Text>
                <Text style={styles.featuredMetaText}>•</Text>
                <Text style={styles.featuredMetaText}>{featuredVideo.creator.username}</Text>
              </View>

              <View style={styles.featuredActions}>
                <TouchableOpacity
                  style={styles.playButton}
                  onPress={() => {
                    hapticFeedback.medium();
                    onVideoPress(featuredVideo);
                  }}
                >
                  <Ionicons name="play" size={24} color="#000000" />
                  <Text style={styles.playButtonText}>Play</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.addToListButton, isInMyList && styles.addToListButtonActive]}
                  onPress={() => {
                    hapticFeedback.light();
                    setIsInMyList(!isInMyList);
                  }}
                >
                  <Ionicons
                    name={isInMyList ? "checkmark" : "add"}
                    size={24}
                    color="#FFFFFF"
                  />
                  <Text style={styles.addToListButtonText}>
                    {isInMyList ? "In My List" : "My List"}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.infoButton}
                  onPress={() => {
                    hapticFeedback.light();
                    setShowVideoInfo(true);
                  }}
                >
                  <Ionicons name="information-circle-outline" size={24} color="#FFFFFF" />
                  <Text style={styles.infoButtonText}>More Info</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.shareButton}
                  onPress={() => {
                    hapticFeedback.light();
                    // Handle share
                  }}
                >
                  <Ionicons name="share-outline" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>

          {/* Preview Toggle Button */}
          <TouchableOpacity
            style={styles.previewToggle}
            onPress={() => {
              hapticFeedback.light();
              setIsPreviewPlaying(!isPreviewPlaying);
            }}
          >
            <Ionicons
              name={isPreviewPlaying ? "volume-mute" : "play-circle"}
              size={24}
              color="#FFFFFF"
            />
          </TouchableOpacity>
        </View>
      )}

      {/* Content Rows */}
      <View style={styles.contentSection}>
        {isLoading ? (
          renderLoadingSkeletons()
        ) : (
          <>
            {/* Continue Watching Section */}
            <View style={styles.continueWatchingSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Continue Watching</Text>
                <TouchableOpacity>
                  <Text style={styles.seeAllButton}>See All</Text>
                </TouchableOpacity>
              </View>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.continueWatchingScroll}>
                {/* Add continue watching items here */}
              </ScrollView>
            </View>

            {/* Trending */}
            <VideoRow
              title="Trending Now"
              videos={getTrendingVideos()}
              onVideoPress={onVideoPress}
              size="medium"
            />

            {/* Recommendations */}
            {recommendations.length > 0 && (
              <VideoRow
                title="📺 Recommended for You"
                videos={recommendations}
                onVideoPress={onVideoPress}
                size="medium"
              />
            )}

            {/* Recent */}
            <VideoRow
              title="🆕 Recently Added"
              videos={getRecentVideos()}
              onVideoPress={onVideoPress}
              size="small"
            />

            {/* Categories */}
            {categories.map((category) => {
              const categoryVideos = getVideosByCategory(category.id);
              if (categoryVideos.length === 0) return null;

              return (
                <VideoRow
                  key={category.id}
                  title={category.name}
                  videos={categoryVideos}
                  onVideoPress={onVideoPress}
                  size="medium"
                />
              );
            })}
          </>
        )}
      </View>

      {/* Video Info Modal */}
      <Modal
        visible={showVideoInfo}
        transparent
        animationType="slide"
        onRequestClose={() => setShowVideoInfo(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Video Details</Text>
              <TouchableOpacity
                onPress={() => setShowVideoInfo(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>

            {featuredVideo && (
              <ScrollView style={styles.modalBody}>
                <Image
                  source={{ uri: featuredVideo.thumbnail_url }}
                  style={styles.modalThumbnail}
                  resizeMode="cover"
                />
                <Text style={styles.modalVideoTitle}>{featuredVideo.title}</Text>
                <Text style={styles.modalDescription}>{featuredVideo.description}</Text>

                <View style={styles.modalMeta}>
                  <Text style={styles.modalMetaItem}>Creator: {featuredVideo.creator.username}</Text>
                  <Text style={styles.modalMetaItem}>Views: {formatViewCount(featuredVideo.view_count)}</Text>
                  <Text style={styles.modalMetaItem}>Duration: {formatDuration(featuredVideo.duration)}</Text>
                  <Text style={styles.modalMetaItem}>Category: {featuredVideo.category}</Text>
                </View>

                <View style={styles.modalTags}>
                  {featuredVideo.tags.map((tag, index) => (
                    <View key={index} style={styles.modalTag}>
                      <Text style={styles.modalTagText}>#{tag}</Text>
                    </View>
                  ))}
                </View>

                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.modalPlayButton}
                    onPress={() => {
                      setShowVideoInfo(false);
                      onVideoPress(featuredVideo);
                    }}
                  >
                    <Ionicons name="play" size={20} color="#000000" />
                    <Text style={styles.modalPlayButtonText}>Play Now</Text>
                  </TouchableOpacity>
                </View>
              </ScrollView>
            )}
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  featuredSection: {
    height: width * 1.2,
    position: 'relative',
  },
  featuredImage: {
    width: '100%',
    height: '100%',
  },
  featuredVideo: {
    width: '100%',
    height: '100%',
  },
  maturityBadge: {
    position: 'absolute',
    top: 20,
    left: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    zIndex: 2,
  },
  maturityText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  featuredOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%',
    justifyContent: 'flex-end',
    paddingBottom: 40,
  },
  featuredContent: {
    padding: 20,
  },
  featuredHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featuredTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: -1, height: 1 },
    textShadowRadius: 10,
  },
  featuredDescription: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 20,
    lineHeight: 22,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: -1, height: 1 },
    textShadowRadius: 10,
  },
  featuredPremiumBadge: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: GoGoColors.premium,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    zIndex: 2,
  },
  featuredPremiumText: {
    color: '#000000',
    fontSize: 12,
    fontWeight: 'bold',
  },
  featuredMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  featuredMetaText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  featuredActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 15,
    flexWrap: 'wrap',
  },
  playButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
    justifyContent: 'center',
    minWidth: 120,
  },
  playButtonText: {
    color: '#000000',
    fontSize: 16,
    fontWeight: 'bold',
  },
  addToListButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  addToListButtonActive: {
    backgroundColor: GoGoColors.success,
    borderColor: GoGoColors.success,
  },
  addToListButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  infoButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  infoButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  shareButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  previewToggle: {
    position: 'absolute',
    top: 60,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 8,
    borderRadius: 20,
    zIndex: 2,
  },
  contentSection: {
    paddingBottom: 50,
    marginTop: -50,
  },
  videoRow: {
    marginBottom: 35,
  },
  rowTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 15,
    marginBottom: 15,
  },
  videoList: {
    paddingLeft: 15,
  },
  videoCardContainer: {
    marginRight: 10,
  },
  videoCard: {
    borderRadius: 4,
    overflow: 'hidden',
    backgroundColor: '#1F1F1F',
    position: 'relative',
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40%',
    justifyContent: 'flex-end',
  },
  videoInfo: {
    padding: 10,
  },
  videoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  premiumBadge: {
    backgroundColor: '#E50914',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 2,
    alignSelf: 'flex-start',
  },
  premiumText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  seeAllButton: {
    color: GoGoColors.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  continueWatchingSection: {
    marginBottom: 35,
  },
  continueWatchingScroll: {
    paddingLeft: 15,
  },
  playIconContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    opacity: 0,
  },
  skeletonContainer: {
    backgroundColor: '#2A2A2A',
    overflow: 'hidden',
    position: 'relative',
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    transform: [{ skewX: '-20deg' }],
  },
  previewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#1F1F1F',
    borderRadius: 8,
    overflow: 'hidden',
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  previewImage: {
    width: '100%',
    height: 200,
  },
  previewInfo: {
    padding: 15,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  previewMeta: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 15,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 10,
  },
  previewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 4,
    gap: 8,
  },
  previewButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  scrollButton: {
    position: 'absolute',
    right: 0,
    top: '50%',
    transform: [{ translateY: -20 }],
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  skeletonFeatured: {
    backgroundColor: '#2A2A2A',
    overflow: 'hidden',
  },
  skeletonTitle: {
    width: 150,
    height: 24,
    backgroundColor: '#2A2A2A',
    borderRadius: 4,
    marginLeft: 15,
    marginBottom: 15,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  modalTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalBody: {
    padding: 20,
  },
  modalThumbnail: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  modalVideoTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  modalDescription: {
    color: GoGoColors.textSecondary,
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
  },
  modalMeta: {
    marginBottom: 20,
  },
  modalMetaItem: {
    color: GoGoColors.textSecondary,
    fontSize: 14,
    marginBottom: 8,
  },
  modalTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 24,
  },
  modalTag: {
    backgroundColor: GoGoColors.glassBg,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  modalTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  modalActions: {
    paddingBottom: 20,
  },
  modalPlayButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  modalPlayButtonText: {
    color: '#000000',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
