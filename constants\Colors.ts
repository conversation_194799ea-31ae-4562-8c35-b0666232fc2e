// StreamFlix Enhanced Color Scheme
const primaryAccent = '#D4AF37';
const backgroundDark = '#121212';
const textLight = '#F5F5F5';
const highlightGold = '#FFD700';

const tintColorLight = primaryAccent;
const tintColorDark = highlightGold;

export default {
  light: {
    text: '#000',
    background: '#fff',
    tint: tintColorLight,
    tabIconDefault: '#999',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#000',
    background: '#fff',
    tint: tintColorLight,
    tabIconDefault: '#999',
    tabIconSelected: tintColorLight,
  },
};

// Enhanced GoGo Light Theme Colors
export const GoGoColors = {
  // Primary colors
  primary: '#E50914',
  primaryDark: '#B20710',
  primaryLight: '#FF1E2D',
  primaryAccent,

  // Background colors (Light Theme)
  backgroundDark: '#FFFFFF',
  backgroundMedium: '#F8F9FA',
  backgroundLight: '#F1F3F4',
  backgroundCard: '#FFFFFF',

  // Background gradients (Light Theme)
  backgroundGradient: ['#FFFFFF', '#F8F9FA', '#FFFFFF'],
  cardGradient: ['rgba(0,0,0,0.05)', 'rgba(0,0,0,0.02)'],
  heroGradient: ['rgba(255,255,255,0.8)', 'rgba(255,255,255,0.95)'],

  // Text colors (Light Theme)
  textLight: '#000000',
  textPrimary: '#000000',
  textSecondary: '#333333',
  textMuted: '#666666',
  textDisabled: '#999999',

  // Accent colors
  gold: '#FFD700',
  goldDark: '#DAA520',
  highlightGold,

  // Status colors
  success: '#28a745',
  warning: '#ffc107',
  error: '#dc3545',
  info: '#17a2b8',

  // Border colors (Light Theme)
  border: '#E0E0E0',
  borderLight: '#F0F0F0',
  borderAccent: '#CCCCCC',

  // Glass morphism effects (Light Theme)
  glassBg: 'rgba(0, 0, 0, 0.05)',
  glassBlur: 'rgba(0, 0, 0, 0.02)',
  glassBorder: 'rgba(0, 0, 0, 0.1)',

  // Overlay colors (Light Theme)
  overlayDark: 'rgba(255, 255, 255, 0.9)',
  overlayLight: 'rgba(255, 255, 255, 0.7)',
  overlayMedium: 'rgba(255, 255, 255, 0.8)',

  // Interactive states (Light Theme)
  hover: 'rgba(0, 0, 0, 0.05)',
  pressed: 'rgba(0, 0, 0, 0.1)',
  disabled: 'rgba(0, 0, 0, 0.3)',

  // Premium/subscription colors
  premium: '#FFD700',
  premiumGradient: ['#FFD700', '#FFA500'],

  // Rating colors (Light Theme)
  ratingGold: '#FFD700',
  ratingEmpty: '#E0E0E0',
};
