// GoGo Twitter-Inspired Color Scheme
const twitterBlue = '#1DA1F2';
const twitterDarkBlue = '#1A91DA';
const twitterLightBlue = '#E1F5FE';
const primaryAccent = '#42A5F5';
const backgroundDark = '#121212';
const textLight = '#F5F5F5';
const highlightGold = '#F7931E';

const tintColorLight = primaryAccent;
const tintColorDark = highlightGold;

export default {
  light: {
    text: '#000',
    background: '#fff',
    tint: tintColorLight,
    tabIconDefault: '#999',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#000',
    background: '#fff',
    tint: tintColorLight,
    tabIconDefault: '#999',
    tabIconSelected: tintColorLight,
  },
};

// Enhanced GoGo Twitter-Inspired Theme Colors
export const GoGoColors = {
  // Primary colors (Twitter Blue)
  primary: twitterBlue,
  primaryDark: twitterDarkBlue,
  primaryLight: '#64B5F6',
  primaryAccent,

  // Background colors (Clean & Modern)
  backgroundDark: '#FFFFFF',
  backgroundMedium: '#F7F9FA',
  backgroundLight: twitterLightBlue,
  backgroundCard: '#FFFFFF',

  // Background gradients (Subtle & Clean)
  backgroundGradient: ['#FFFFFF', '#F7F9FA', '#FFFFFF'],
  cardGradient: ['rgba(29,161,242,0.03)', 'rgba(29,161,242,0.01)'],
  heroGradient: ['rgba(29,161,242,0.1)', 'rgba(29,161,242,0.05)'],

  // Text colors (Twitter-inspired)
  textLight: '#FFFFFF',
  textPrimary: '#0F1419',
  textSecondary: '#536471',
  textMuted: '#8B98A5',
  textDisabled: '#CDD1D6',

  // Accent colors (Twitter-inspired)
  gold: '#FFD700',
  goldDark: '#DAA520',
  highlightGold,

  // Status colors (Twitter-inspired)
  success: '#00BA7C',
  warning: '#FFAD1F',
  error: '#F4212E',
  info: twitterBlue,

  // Border colors (Clean & Modern)
  border: '#EFF3F4',
  borderLight: '#F7F9FA',
  borderAccent: '#CFD9DE',

  // Glass morphism effects (Twitter-inspired)
  glassBg: 'rgba(29, 161, 242, 0.08)',
  glassBlur: 'rgba(29, 161, 242, 0.04)',
  glassBorder: 'rgba(29, 161, 242, 0.15)',

  // Overlay colors (Clean)
  overlayDark: 'rgba(15, 20, 25, 0.75)',
  overlayLight: 'rgba(255, 255, 255, 0.85)',
  overlayMedium: 'rgba(247, 249, 250, 0.9)',

  // Interactive states (Twitter-inspired)
  hover: 'rgba(29, 161, 242, 0.1)',
  pressed: 'rgba(29, 161, 242, 0.2)',
  disabled: 'rgba(83, 100, 113, 0.5)',

  // Premium/subscription colors (Unique)
  premium: '#F7931E',
  premiumGradient: ['#F7931E', '#FF6B35'],

  // Rating colors (Modern)
  ratingGold: '#F7931E',
  ratingEmpty: '#CFD9DE',
};
