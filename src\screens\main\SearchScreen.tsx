import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  Animated,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppDispatch, useAppSelector } from '../../store';
import { searchVideos, clearSearchResults, fetchCategories } from '../../store/slices/videoSlice';
import { Video, Category } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { useResponsiveLayout } from '../../utils/responsive';
import { hapticFeedback } from '../../utils/animations';
import VideoCard from '../../components/VideoCard';

const { width } = Dimensions.get('window');

interface VideoSearchResultProps {
  video: Video;
  onPress: (video: Video) => void;
}

function VideoSearchResult({ video, onPress }: VideoSearchResultProps) {
  return (
    <TouchableOpacity style={styles.searchResult} onPress={() => onPress(video)}>
      <Image source={{ uri: video.thumbnail_url }} style={styles.resultThumbnail} />
      <View style={styles.resultInfo}>
        <Text style={styles.resultTitle} numberOfLines={2}>
          {video.title}
        </Text>
        <Text style={styles.resultCreator}>{video.creator.username}</Text>
        <View style={styles.resultStats}>
          <Text style={styles.resultStat}>{video.view_count} views</Text>
          <Text style={styles.resultStat}>•</Text>
          <Text style={styles.resultStat}>{video.category.name}</Text>
          {video.is_premium && (
            <>
              <Text style={styles.resultStat}>•</Text>
              <View style={styles.premiumBadge}>
                <Text style={styles.premiumText}>PREMIUM</Text>
              </View>
            </>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}

interface CategoryChipProps {
  category: Category;
  isSelected: boolean;
  onPress: (category: Category) => void;
}

function CategoryChip({ category, isSelected, onPress }: CategoryChipProps) {
  return (
    <TouchableOpacity
      style={[styles.categoryChip, isSelected && styles.selectedCategoryChip]}
      onPress={() => onPress(category)}
    >
      <Text style={[styles.categoryChipText, isSelected && styles.selectedCategoryChipText]}>
        {category.name}
      </Text>
    </TouchableOpacity>
  );
}

interface Props {
  onVideoPress: (video: Video) => void;
}

export default function SearchScreen({ onVideoPress }: Props) {
  const dispatch = useAppDispatch();
  const { searchResults, categories } = useAppSelector((state) => state.video);
  const layout = useResponsiveLayout();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([
    'Action movies',
    'Comedy shows',
    'Sci-fi series',
    'Documentary',
  ]);
  const [filters, setFilters] = useState({
    duration: 'any',
    quality: 'any',
    price: 'any',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [isVoiceSearchActive, setIsVoiceSearchActive] = useState(false);

  const searchInputRef = useRef<TextInput>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Load categories on mount
    dispatch(fetchCategories());
  }, [dispatch]);

  useEffect(() => {
    // Debounced search
    if (searchQuery.trim().length > 2) {
      setIsSearching(true);
      const timeoutId = setTimeout(() => {
        dispatch(searchVideos(searchQuery.trim()));
        setIsSearching(false);
      }, 500);

      return () => clearTimeout(timeoutId);
    } else {
      dispatch(clearSearchResults());
    }
  }, [searchQuery, dispatch]);

  const handleCategoryPress = (category: Category) => {
    if (selectedCategory?.id === category.id) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(category);
      // You could implement category-specific search here
    }
  };

  const clearSearch = () => {
    hapticFeedback.light();
    setSearchQuery('');
    setSelectedCategory(null);
    dispatch(clearSearchResults());
    searchInputRef.current?.blur();
  };

  const handleRecentSearchPress = (search: string) => {
    hapticFeedback.light();
    setSearchQuery(search);
    searchInputRef.current?.focus();
  };

  const removeRecentSearch = (search: string) => {
    hapticFeedback.light();
    setRecentSearches(prev => prev.filter(item => item !== search));
  };

  const handleVoiceSearch = () => {
    hapticFeedback.medium();
    setIsVoiceSearchActive(!isVoiceSearchActive);
    // Voice search implementation would go here
  };

  const toggleFilters = () => {
    hapticFeedback.light();
    setShowFilters(!showFilters);
    Animated.timing(fadeAnim, {
      toValue: showFilters ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const renderSearchResult = ({ item }: { item: Video }) => (
    <VideoSearchResult video={item} onPress={onVideoPress} />
  );

  const renderCategory = ({ item }: { item: Category }) => (
    <CategoryChip
      category={item}
      isSelected={selectedCategory?.id === item.id}
      onPress={handleCategoryPress}
    />
  );

  return (
    <View style={styles.container}>
      {/* Enhanced Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={GoGoColors.textMuted} style={styles.searchIcon} />
          <TextInput
            ref={searchInputRef}
            style={styles.searchInput}
            placeholder="Search movies, shows, creators..."
            placeholderTextColor={GoGoColors.textMuted}
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCorrect={false}
            returnKeyType="search"
            onSubmitEditing={() => {
              if (searchQuery.trim()) {
                handleRecentSearchPress(searchQuery.trim());
              }
            }}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
              <Ionicons name="close" size={20} color={StreamFlixColors.textMuted} />
            </TouchableOpacity>
          )}
        </View>

        {/* Voice Search and Filter Buttons */}
        <View style={styles.searchActions}>
          <TouchableOpacity
            style={[styles.voiceSearchButton, isVoiceSearchActive && styles.voiceSearchActive]}
            onPress={handleVoiceSearch}
          >
            <Ionicons
              name={isVoiceSearchActive ? "mic" : "mic-outline"}
              size={20}
              color={isVoiceSearchActive ? StreamFlixColors.primary : "white"}
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.filterButton} onPress={toggleFilters}>
            <Ionicons name="options" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Chips */}
      {showFilters && (
        <Animated.View style={[styles.filtersContainer, { opacity: fadeAnim }]}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[styles.filterChip, filters.price === 'free' && styles.filterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, price: prev.price === 'free' ? 'any' : 'free' }))}
            >
              <Text style={[styles.filterChipText, filters.price === 'free' && styles.filterChipTextActive]}>
                Free
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterChip, filters.price === 'premium' && styles.filterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, price: prev.price === 'premium' ? 'any' : 'premium' }))}
            >
              <Text style={[styles.filterChipText, filters.price === 'premium' && styles.filterChipTextActive]}>
                Premium
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterChip, filters.duration === 'short' && styles.filterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, duration: prev.duration === 'short' ? 'any' : 'short' }))}
            >
              <Text style={[styles.filterChipText, filters.duration === 'short' && styles.filterChipTextActive]}>
                Short (&lt; 10 min)
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterChip, filters.duration === 'long' && styles.filterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, duration: prev.duration === 'long' ? 'any' : 'long' }))}
            >
              <Text style={[styles.filterChipText, filters.duration === 'long' && styles.filterChipTextActive]}>
                Long (&gt; 30 min)
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </Animated.View>
      )}

      {/* Recent Searches */}
      {searchQuery.length === 0 && recentSearches.length > 0 && (
        <View style={styles.recentSearchesContainer}>
          <Text style={styles.recentSearchesTitle}>Recent Searches</Text>
          {recentSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              style={styles.recentSearchItem}
              onPress={() => handleRecentSearchPress(search)}
            >
              <Ionicons name="time" size={16} color={StreamFlixColors.textMuted} />
              <Text style={styles.recentSearchText}>{search}</Text>
              <TouchableOpacity
                onPress={() => removeRecentSearch(search)}
                style={styles.removeRecentButton}
              >
                <Ionicons name="close" size={16} color={StreamFlixColors.textMuted} />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Categories */}
      <View style={styles.categoriesContainer}>
        <Text style={styles.categoriesTitle}>Browse by Category</Text>
        <FlatList
          data={categories}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item.id}
          renderItem={renderCategory}
          contentContainerStyle={styles.categoriesList}
        />
      </View>

      {/* Search Results */}
      <View style={styles.resultsContainer}>
        {isSearching ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Searching...</Text>
          </View>
        ) : searchQuery.length > 0 ? (
          <>
            <Text style={styles.resultsTitle}>
              {searchResults.length} results for "{searchQuery}"
            </Text>
            <FlatList
              data={searchResults}
              keyExtractor={(item) => item.id}
              renderItem={renderSearchResult}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.resultsList}
            />
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="search" size={80} color={StreamFlixColors.textMuted} />
            <Text style={styles.emptyTitle}>Discover Amazing Content</Text>
            <Text style={styles.emptyText}>
              Search for videos, creators, or browse by category to find your next favorite content.
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: StreamFlixColors.backgroundDark,
  },
  searchContainer: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: StreamFlixColors.backgroundMedium,
    borderRadius: 25,
    paddingHorizontal: 15,
    height: 50,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    color: StreamFlixColors.textLight,
    fontSize: 16,
  },
  clearButton: {
    padding: 5,
  },
  categoriesContainer: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
  },
  categoriesTitle: {
    color: StreamFlixColors.textLight,
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 15,
    marginBottom: 10,
  },
  categoriesList: {
    paddingHorizontal: 15,
  },
  categoryChip: {
    backgroundColor: StreamFlixColors.backgroundLight,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  selectedCategoryChip: {
    backgroundColor: StreamFlixColors.primaryAccent,
  },
  categoryChipText: {
    color: StreamFlixColors.textSecondary,
    fontSize: 14,
    fontWeight: '500',
  },
  selectedCategoryChipText: {
    color: StreamFlixColors.backgroundDark,
  },
  resultsContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: StreamFlixColors.textSecondary,
    fontSize: 16,
  },
  resultsTitle: {
    color: StreamFlixColors.textLight,
    fontSize: 16,
    fontWeight: 'bold',
    margin: 15,
  },
  resultsList: {
    paddingBottom: 20,
  },
  searchResult: {
    flexDirection: 'row',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.backgroundMedium,
  },
  resultThumbnail: {
    width: 120,
    height: 68,
    borderRadius: 8,
    backgroundColor: StreamFlixColors.backgroundLight,
  },
  resultInfo: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'space-between',
  },
  resultTitle: {
    color: StreamFlixColors.textLight,
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 20,
  },
  resultCreator: {
    color: StreamFlixColors.textSecondary,
    fontSize: 14,
    marginTop: 4,
  },
  resultStats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  resultStat: {
    color: StreamFlixColors.textMuted,
    fontSize: 12,
    marginRight: 8,
  },
  premiumBadge: {
    backgroundColor: StreamFlixColors.primaryAccent,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 3,
  },
  premiumText: {
    color: StreamFlixColors.backgroundDark,
    fontSize: 10,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    color: StreamFlixColors.textLight,
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  emptyText: {
    color: StreamFlixColors.textMuted,
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  // New enhanced search styles
  searchActions: {
    flexDirection: 'row',
    gap: 8,
  },
  voiceSearchButton: {
    backgroundColor: StreamFlixColors.glassBg,
    padding: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: StreamFlixColors.glassBorder,
  },
  voiceSearchActive: {
    backgroundColor: StreamFlixColors.primary,
    borderColor: StreamFlixColors.primary,
  },
  filterButton: {
    backgroundColor: StreamFlixColors.glassBg,
    padding: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: StreamFlixColors.glassBorder,
  },
  filtersContainer: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
  },
  filterChip: {
    backgroundColor: StreamFlixColors.glassBg,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: StreamFlixColors.glassBorder,
  },
  filterChipActive: {
    backgroundColor: StreamFlixColors.primary,
    borderColor: StreamFlixColors.primary,
  },
  filterChipText: {
    color: StreamFlixColors.textSecondary,
    fontSize: 14,
    fontWeight: '500',
  },
  filterChipTextActive: {
    color: StreamFlixColors.textLight,
    fontWeight: '600',
  },
  recentSearchesContainer: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
  },
  recentSearchesTitle: {
    color: StreamFlixColors.textLight,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  recentSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 4,
  },
  recentSearchText: {
    color: StreamFlixColors.textSecondary,
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  removeRecentButton: {
    padding: 4,
  },
});
