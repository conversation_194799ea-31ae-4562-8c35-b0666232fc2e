import { Dimensions } from 'react-native';
import { useEffect, useState } from 'react';

export interface ResponsiveLayout {
  width: number;
  height: number;
  isTablet: boolean;
  isLandscape: boolean;
  cardWidth: number;
  gridColumns: number;
  isSmallScreen: boolean;
  isMediumScreen: boolean;
  isLargeScreen: boolean;
}

export const useResponsiveLayout = (): ResponsiveLayout => {
  const [dimensions, setDimensions] = useState(Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  const { width, height } = dimensions;
  const isTablet = width > 768;
  const isLandscape = width > height;
  const isSmallScreen = width < 375;
  const isMediumScreen = width >= 375 && width < 768;
  const isLargeScreen = width >= 768;

  const cardWidth = isTablet 
    ? width * 0.25 
    : isLandscape 
      ? width * 0.3 
      : width * 0.4;

  const gridColumns = isTablet ? 4 : isLandscape ? 3 : 2;

  return {
    width,
    height,
    isTablet,
    isLandscape,
    cardWidth,
    gridColumns,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
  };
};

export const getResponsiveSize = (
  small: number,
  medium: number,
  large: number,
  layout: ResponsiveLayout
): number => {
  if (layout.isLargeScreen) return large;
  if (layout.isMediumScreen) return medium;
  return small;
};

export const getResponsivePadding = (layout: ResponsiveLayout) => ({
  horizontal: layout.isTablet ? 24 : 16,
  vertical: layout.isTablet ? 20 : 12,
  small: layout.isTablet ? 12 : 8,
  medium: layout.isTablet ? 16 : 12,
  large: layout.isTablet ? 24 : 16,
});

export const getResponsiveFontSize = (layout: ResponsiveLayout) => ({
  small: layout.isTablet ? 14 : 12,
  medium: layout.isTablet ? 16 : 14,
  large: layout.isTablet ? 20 : 18,
  xlarge: layout.isTablet ? 24 : 22,
  xxlarge: layout.isTablet ? 32 : 28,
});
