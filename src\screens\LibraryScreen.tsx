import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, FlatList, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../store';
import { Text, Button, Card, List, Divider } from 'react-native-paper';
import { GoGoColors } from '../../constants/Colors';
import { Video, User } from '../types';
import { useResponsiveLayout } from '../utils/responsive';
import { hapticFeedback } from '../utils/animations';
import { formatViewCount, formatRelativeTime } from '../utils/formatters';

const { width } = Dimensions.get('window');

interface ListIconProps {
  icon: string;
  color?: string;
  size?: number;
}

const LibraryScreen = () => {
  const layout = useResponsiveLayout();
  const [activeTab, setActiveTab] = useState('playlists');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('recent');
  const { watchLater, playlists, favoriteCreators, viewingHistory, contentPreferences } = useAppSelector(
    (state) => state.userPreferences
  );

  const renderPlaylistsTab = () => (
    <View style={styles.tabContent}>
      <Text variant="titleLarge" style={[styles.sectionTitle, { color: GoGoColors.textLight }]}>
        Watch Later
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
        {watchLater.map((video: Video) => (
          <Card key={video.id} style={[styles.videoCard, { backgroundColor: GoGoColors.backgroundDark }]}>
            <Card.Cover source={{ uri: video.thumbnail_url }} />
            <Card.Title
              title={video.title}
              subtitle={video.creator.username}
              titleStyle={{ color: GoGoColors.textLight }}
              subtitleStyle={{ color: GoGoColors.textMuted }}
            />
          </Card>
        ))}
      </ScrollView>

      <Text variant="titleLarge" style={[styles.sectionTitle, { color: GoGoColors.textLight }]}>
        Your Playlists
      </Text>
      {playlists.map((playlist) => (
        <Card key={playlist.id} style={[styles.playlistCard, { backgroundColor: GoGoColors.backgroundDark }]}>
          <Card.Title
            title={playlist.name}
            subtitle={playlist.description}
            titleStyle={{ color: GoGoColors.textLight }}
            subtitleStyle={{ color: GoGoColors.textMuted }}
          />
          <Card.Content>
            <Text style={{ color: GoGoColors.textLight }}>{playlist.videos.length} videos</Text>
          </Card.Content>
        </Card>
      ))}
    </View>
  );

  const renderPreferencesTab = () => (
    <View style={styles.tabContent}>
      <List.Section>
        <List.Subheader style={{ color: GoGoColors.textLight }}>Content Preferences</List.Subheader>
        <List.Item
          title="Video Quality"
          description={contentPreferences.quality}
          left={(props: ListIconProps) => <List.Icon {...props} icon="video-quality" color={GoGoColors.highlightGold} />}
          titleStyle={{ color: GoGoColors.textLight }}
          descriptionStyle={{ color: GoGoColors.textMuted }}
        />
        <List.Item
          title="Autoplay"
          description={contentPreferences.autoplay ? 'Enabled' : 'Disabled'}
          left={(props: ListIconProps) => <List.Icon {...props} icon="play" color={GoGoColors.highlightGold} />}
          titleStyle={{ color: GoGoColors.textLight }}
          descriptionStyle={{ color: GoGoColors.textMuted }}
        />
        <List.Item
          title="Notifications"
          description={contentPreferences.notifications ? 'Enabled' : 'Disabled'}
          left={(props: ListIconProps) => <List.Icon {...props} icon="bell" color={GoGoColors.highlightGold} />}
          titleStyle={{ color: GoGoColors.textLight }}
          descriptionStyle={{ color: GoGoColors.textMuted }}
        />
      </List.Section>
    </View>
  );

  const renderHistoryTab = () => (
    <View style={styles.tabContent}>
      {viewingHistory.map((item) => (
        <Card key={item.video.id} style={[styles.historyCard, { backgroundColor: GoGoColors.backgroundDark }]}>
          <Card.Cover source={{ uri: item.video.thumbnail_url }} />
          <Card.Title
            title={item.video.title}
            subtitle={`Last watched: ${new Date(item.lastWatched).toLocaleDateString()}`}
            titleStyle={{ color: GoGoColors.textLight }}
            subtitleStyle={{ color: GoGoColors.textMuted }}
          />
          <Card.Content>
            <Text style={{ color: GoGoColors.textLight }}>
              Progress: {Math.round(item.progress * 100)}%
            </Text>
          </Card.Content>
        </Card>
      ))}
    </View>
  );

  const renderFavoriteCreatorsTab = () => (
    <View style={styles.tabContent}>
      {favoriteCreators.map((creator: User) => (
        <Card key={creator.id} style={[styles.creatorCard, { backgroundColor: GoGoColors.backgroundDark }]}>
          <Card.Title
            title={creator.username}
            subtitle={creator.full_name}
            titleStyle={{ color: GoGoColors.textLight }}
            subtitleStyle={{ color: GoGoColors.textMuted }}
            left={(props: ListIconProps) => (
              <Card.Cover
                {...props}
                source={{ uri: creator.avatar_url || 'https://via.placeholder.com/40' }}
                style={styles.creatorAvatar}
              />
            )}
          />
        </Card>
      ))}
    </View>
  );

  const TabButton = ({ title, isActive, onPress, icon, count }: {
    title: string;
    isActive: boolean;
    onPress: () => void;
    icon: string;
    count?: number;
  }) => (
    <TouchableOpacity
      style={[styles.enhancedTab, isActive && styles.enhancedActiveTab]}
      onPress={() => {
        hapticFeedback.light();
        onPress();
      }}
    >
      <View style={styles.tabContent}>
        <Ionicons
          name={icon as any}
          size={20}
          color={isActive ? GoGoColors.primary : GoGoColors.textMuted}
        />
        <Text style={[styles.enhancedTabText, isActive && styles.enhancedActiveTabText]}>
          {title}
        </Text>
        {count !== undefined && count > 0 && (
          <View style={styles.tabBadge}>
            <Text style={styles.tabBadgeText}>{count}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: GoGoColors.backgroundDark }]}>
      {/* Enhanced Tab Bar */}
      <View style={[styles.enhancedTabBar, { borderBottomColor: GoGoColors.border }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabScroll}>
          <TabButton
            title="My List"
            isActive={activeTab === 'playlists'}
            onPress={() => setActiveTab('playlists')}
            icon="bookmark"
            count={watchLater.length + playlists.length}
          />
          <TabButton
            title="Downloads"
            isActive={activeTab === 'preferences'}
            onPress={() => setActiveTab('preferences')}
            icon="download"
            count={0}
          />
          <TabButton
            title="History"
            isActive={activeTab === 'history'}
            onPress={() => setActiveTab('history')}
            icon="time"
            count={viewingHistory.length}
          />
          <TabButton
            title="Creators"
            isActive={activeTab === 'creators'}
            onPress={() => setActiveTab('creators')}
            icon="people"
            count={favoriteCreators.length}
          />
        </ScrollView>
      </View>

      {/* Controls */}
      {(activeTab === 'playlists' || activeTab === 'history') && (
        <View style={styles.controlsContainer}>
          <View style={styles.sortContainer}>
            <Text style={styles.sortLabel}>Sort by:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <TouchableOpacity
                style={[styles.sortButton, sortBy === 'recent' && styles.activeSortButton]}
                onPress={() => {
                  hapticFeedback.light();
                  setSortBy('recent');
                }}
              >
                <Text style={[styles.sortButtonText, sortBy === 'recent' && styles.activeSortButtonText]}>
                  Recent
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortButton, sortBy === 'title' && styles.activeSortButton]}
                onPress={() => {
                  hapticFeedback.light();
                  setSortBy('title');
                }}
              >
                <Text style={[styles.sortButtonText, sortBy === 'title' && styles.activeSortButtonText]}>
                  Title
                </Text>
              </TouchableOpacity>
            </ScrollView>
          </View>

          <TouchableOpacity
            style={styles.viewToggle}
            onPress={() => {
              hapticFeedback.light();
              setViewMode(viewMode === 'grid' ? 'list' : 'grid');
            }}
          >
            <Ionicons
              name={viewMode === 'grid' ? 'list' : 'grid'}
              size={20}
              color="white"
            />
          </TouchableOpacity>
        </View>
      )}

      {activeTab === 'playlists' && renderPlaylistsTab()}
      {activeTab === 'preferences' && renderPreferencesTab()}
      {activeTab === 'history' && renderHistoryTab()}
      {activeTab === 'creators' && renderFavoriteCreatorsTab()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    flexDirection: 'row',
    padding: 8,
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  horizontalScroll: {
    marginBottom: 24,
  },
  videoCard: {
    width: 200,
    marginRight: 16,
  },
  playlistCard: {
    marginBottom: 16,
  },
  historyCard: {
    marginBottom: 16,
  },
  creatorCard: {
    marginBottom: 16,
  },
  creatorAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: 8,
  },
  // Enhanced tab styles
  enhancedTabBar: {
    borderBottomWidth: 1,
    paddingVertical: 8,
  },
  tabScroll: {
    paddingHorizontal: 16,
  },
  enhancedTab: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: GoGoColors.glassBg,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  enhancedActiveTab: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  enhancedTabText: {
    color: GoGoColors.textMuted,
    fontSize: 14,
    fontWeight: '500',
  },
  enhancedActiveTabText: {
    color: GoGoColors.textLight,
    fontWeight: '600',
  },
  tabBadge: {
    backgroundColor: GoGoColors.error,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    color: GoGoColors.textLight,
    fontSize: 10,
    fontWeight: 'bold',
  },
  // Controls styles
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sortLabel: {
    color: GoGoColors.textSecondary,
    fontSize: 14,
    marginRight: 12,
  },
  sortButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  activeSortButton: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  sortButtonText: {
    color: GoGoColors.textSecondary,
    fontSize: 12,
    fontWeight: '500',
  },
  activeSortButtonText: {
    color: GoGoColors.textLight,
    fontWeight: '600',
  },
  viewToggle: {
    backgroundColor: GoGoColors.glassBg,
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
});

export default LibraryScreen;
