import { Video, Category, User } from '../types';

// Mock user for demo mode
export const mockUser: User = {
  id: 'demo-user-1',
  email: '<EMAIL>',
  username: 'demo_user',
  full_name: 'Demo User',
  avatar_url: 'https://via.placeholder.com/150',
  user_type: 'viewer',
  subscription_status: 'active',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

// Mock categories
export const mockCategories: Category[] = [
  {
    id: 'cat-1',
    name: 'Action & Adventure',
    description: 'High-octane thrills and excitement',
    thumbnail_url: 'https://via.placeholder.com/300x200',
    created_at: new Date().toISOString(),
  },
  {
    id: 'cat-2',
    name: 'Comedy',
    description: 'Laugh-out-loud entertainment',
    thumbnail_url: 'https://via.placeholder.com/300x200',
    created_at: new Date().toISOString(),
  },
  {
    id: 'cat-3',
    name: 'Drama',
    description: 'Compelling stories and characters',
    thumbnail_url: 'https://via.placeholder.com/300x200',
    created_at: new Date().toISOString(),
  },
  {
    id: 'cat-4',
    name: 'Sci-Fi',
    description: 'Futuristic and otherworldly content',
    thumbnail_url: 'https://via.placeholder.com/300x200',
    created_at: new Date().toISOString(),
  },
];

// Mock videos
export const mockVideos: Video[] = [
  {
    id: 'video-1',
    title: 'Epic Adventure: The Journey Begins',
    description: 'Follow our heroes as they embark on an incredible journey through mystical lands filled with danger and wonder.',
    thumbnail_url: 'https://via.placeholder.com/400x225',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    duration: 596, // 9:56
    creator_id: 'creator-1',
    creator: {
      id: 'creator-1',
      email: '<EMAIL>',
      username: 'epic_creator',
      full_name: 'Epic Creator',
      avatar_url: 'https://via.placeholder.com/100',
      user_type: 'creator',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    category_id: 'cat-1',
    category: mockCategories[0],
    price: null,
    is_premium: false,
    view_count: 15420,
    like_count: 1250,
    created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    updated_at: new Date(Date.now() - 86400000).toISOString(),
    tags: ['adventure', 'fantasy', 'action'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', file_size: 50000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', file_size: 100000000 },
      { quality: '1080p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', file_size: 200000000 },
    ],
  },
  {
    id: 'video-2',
    title: 'Comedy Gold: Laugh Till You Cry',
    description: 'The funniest compilation of moments that will have you rolling on the floor with laughter.',
    thumbnail_url: 'https://via.placeholder.com/400x225',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    duration: 653, // 10:53
    creator_id: 'creator-2',
    creator: {
      id: 'creator-2',
      email: '<EMAIL>',
      username: 'funny_person',
      full_name: 'Comedy Master',
      avatar_url: 'https://via.placeholder.com/100',
      user_type: 'creator',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    category_id: 'cat-2',
    category: mockCategories[1],
    price: 2.99,
    is_premium: true,
    view_count: 8930,
    like_count: 890,
    created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    updated_at: new Date(Date.now() - 172800000).toISOString(),
    tags: ['comedy', 'funny', 'entertainment'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4', file_size: 45000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4', file_size: 90000000 },
    ],
  },
  {
    id: 'video-3',
    title: 'Drama Masterpiece: Hearts and Minds',
    description: 'A deeply moving story about love, loss, and the human condition that will touch your soul.',
    thumbnail_url: 'https://via.placeholder.com/400x225',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    duration: 15, // 0:15 (short demo)
    creator_id: 'creator-3',
    creator: {
      id: 'creator-3',
      email: '<EMAIL>',
      username: 'drama_queen',
      full_name: 'Drama Director',
      avatar_url: 'https://via.placeholder.com/100',
      user_type: 'creator',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    category_id: 'cat-3',
    category: mockCategories[2],
    price: null,
    is_premium: false,
    view_count: 12340,
    like_count: 1890,
    created_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
    updated_at: new Date(Date.now() - 259200000).toISOString(),
    tags: ['drama', 'emotional', 'story'],
    quality_options: [
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4', file_size: 5000000 },
      { quality: '1080p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4', file_size: 10000000 },
    ],
  },
  {
    id: 'video-4',
    title: 'Sci-Fi Spectacular: Future Worlds',
    description: 'Explore distant galaxies and advanced civilizations in this mind-bending science fiction adventure.',
    thumbnail_url: 'https://via.placeholder.com/400x225',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    duration: 15, // 0:15 (short demo)
    creator_id: 'creator-4',
    creator: {
      id: 'creator-4',
      email: '<EMAIL>',
      username: 'sci_fi_master',
      full_name: 'Future Visionary',
      avatar_url: 'https://via.placeholder.com/100',
      user_type: 'creator',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    category_id: 'cat-4',
    category: mockCategories[3],
    price: 4.99,
    is_premium: true,
    view_count: 20150,
    like_count: 2340,
    created_at: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
    updated_at: new Date(Date.now() - 345600000).toISOString(),
    tags: ['sci-fi', 'future', 'space'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4', file_size: 8000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4', file_size: 15000000 },
      { quality: '1080p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4', file_size: 30000000 },
    ],
  },
  {
    id: 'video-5',
    title: 'Action Packed: High Speed Chase',
    description: 'Non-stop action and adrenaline-pumping sequences that will keep you on the edge of your seat.',
    thumbnail_url: 'https://via.placeholder.com/400x225',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
    duration: 15, // 0:15 (short demo)
    creator_id: 'creator-1',
    creator: {
      id: 'creator-1',
      email: '<EMAIL>',
      username: 'epic_creator',
      full_name: 'Epic Creator',
      avatar_url: 'https://via.placeholder.com/100',
      user_type: 'creator',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    category_id: 'cat-1',
    category: mockCategories[0],
    price: null,
    is_premium: false,
    view_count: 18750,
    like_count: 1650,
    created_at: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
    updated_at: new Date(Date.now() - 432000000).toISOString(),
    tags: ['action', 'chase', 'thriller'],
    quality_options: [
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4', file_size: 12000000 },
      { quality: '1080p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4', file_size: 25000000 },
    ],
  },
];
