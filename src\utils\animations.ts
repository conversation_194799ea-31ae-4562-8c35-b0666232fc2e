import { useSharedValue, withSpring, withTiming, withRepeat, interpolate, Extrapolate } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Animation configurations
export const springConfig = {
  damping: 15,
  stiffness: 150,
  mass: 1,
};

export const timingConfig = {
  duration: 300,
};

export const shimmerConfig = {
  duration: 1000,
  repeat: -1,
  repeatMode: 'reverse' as const,
};

// Common animation hooks
export const useScaleAnimation = (initialValue = 1) => {
  const scale = useSharedValue(initialValue);

  const scaleIn = () => {
    scale.value = withSpring(1.05, springConfig);
  };

  const scaleOut = () => {
    scale.value = withSpring(1, springConfig);
  };

  const scalePress = () => {
    scale.value = withSpring(0.95, springConfig);
  };

  return { scale, scaleIn, scaleOut, scalePress };
};

export const useOpacityAnimation = (initialValue = 1) => {
  const opacity = useSharedValue(initialValue);

  const fadeIn = () => {
    opacity.value = withTiming(1, timingConfig);
  };

  const fadeOut = () => {
    opacity.value = withTiming(0, timingConfig);
  };

  const fadeToggle = () => {
    opacity.value = withTiming(opacity.value === 1 ? 0 : 1, timingConfig);
  };

  return { opacity, fadeIn, fadeOut, fadeToggle };
};

export const useShimmerAnimation = () => {
  const shimmerValue = useSharedValue(0);

  const startShimmer = () => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: shimmerConfig.duration }),
      shimmerConfig.repeat,
      true
    );
  };

  const stopShimmer = () => {
    shimmerValue.value = 0;
  };

  const getShimmerStyle = (width: number) => ({
    transform: [
      {
        translateX: interpolate(
          shimmerValue.value,
          [0, 1],
          [-width, width],
          Extrapolate.CLAMP
        ),
      },
    ],
  });

  return { shimmerValue, startShimmer, stopShimmer, getShimmerStyle };
};

// Haptic feedback utilities
export const hapticFeedback = {
  light: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light),
  medium: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
  heavy: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy),
  selection: () => Haptics.selectionAsync(),
  success: () => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),
  warning: () => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning),
  error: () => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
};

// Animation presets
export const animationPresets = {
  cardHover: {
    scale: 1.05,
    duration: 200,
  },
  buttonPress: {
    scale: 0.95,
    duration: 100,
  },
  fadeIn: {
    opacity: 1,
    duration: 300,
  },
  slideUp: {
    translateY: 0,
    duration: 300,
  },
};
