import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Replace these with your actual Supabase project credentials
// For now, using placeholder values to prevent URL errors
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// Only create client if we have real credentials
export const supabase = SUPABASE_URL.includes('placeholder')
  ? null
  : createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      auth: {
        storage: AsyncStorage,
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
    });

// Database table names
export const TABLES = {
  USERS: 'users',
  VIDEOS: 'videos',
  CATEGORIES: 'categories',
  PURCHASES: 'purchases',
  SUBSCRIPTIONS: 'subscriptions',
  VIDEO_PROGRESS: 'video_progress',
  PLAYLISTS: 'playlists',
  PLAYLIST_VIDEOS: 'playlist_videos',
  NOTIFICATIONS: 'notifications',
  CREATOR_ANALYTICS: 'creator_analytics',
} as const;

// Storage buckets
export const STORAGE_BUCKETS = {
  VIDEOS: 'videos',
  THUMBNAILS: 'thumbnails',
  AVATARS: 'avatars',
} as const;
