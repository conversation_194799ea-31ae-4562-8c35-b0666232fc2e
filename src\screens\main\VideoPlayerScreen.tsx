import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Alert,
  Modal,
  ScrollView,
  Slider,
} from 'react-native';
import { Video } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setPlaying,
  setCurrentTime,
  setDuration,
  setFullscreen,
  setBuffering,
  resetPlayer,
} from '../../store/slices/playerSlice';
import { fetchVideoById, incrementViewCount } from '../../store/slices/videoSlice';
import { Video as VideoType } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { useResponsiveLayout } from '../../utils/responsive';
import { hapticFeedback } from '../../utils/animations';
import { formatTime } from '../../utils/formatters';

const { width, height } = Dimensions.get('window');

interface Props {
  videoId: string;
  onClose: () => void;
}

export default function VideoPlayerScreen({ videoId, onClose }: Props) {
  const dispatch = useAppDispatch();
  const { currentVideo } = useAppSelector((state) => state.video);
  const { isPlaying, currentTime, duration, isFullscreen } = useAppSelector((state) => state.player);
  const { user } = useAppSelector((state) => state.auth);
  const layout = useResponsiveLayout();

  const videoRef = useRef<Video>(null);
  const [showControls, setShowControls] = useState(true);
  const [hasViewBeenCounted, setHasViewBeenCounted] = useState(false);
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);
  const [subtitles, setSubtitles] = useState(false);
  const [quality, setQuality] = useState('auto');
  const [volume, setVolume] = useState(1.0);
  const [brightness, setBrightness] = useState(1.0);
  const [isBuffering, setIsBuffering] = useState(false);
  const [showSpeedSelector, setShowSpeedSelector] = useState(false);
  const [showQualitySelector, setShowQualitySelector] = useState(false);

  useEffect(() => {
    // Load video data
    dispatch(fetchVideoById(videoId));
    
    // Reset player state
    dispatch(resetPlayer());

    return () => {
      dispatch(resetPlayer());
    };
  }, [dispatch, videoId]);

  useEffect(() => {
    // Auto-hide controls after 3 seconds
    if (showControls && isPlaying) {
      const timer = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showControls, isPlaying]);

  useEffect(() => {
    // Count view when video reaches 30 seconds or 25% of duration
    if (currentVideo && currentTime > 30 && !hasViewBeenCounted) {
      dispatch(incrementViewCount(currentVideo.id));
      setHasViewBeenCounted(true);
    }
  }, [currentTime, currentVideo, hasViewBeenCounted, dispatch]);

  const handlePlayPause = async () => {
    hapticFeedback.light();
    if (videoRef.current) {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
      dispatch(setPlaying(!isPlaying));
    }
  };

  const handleSeek = async (position: number) => {
    if (videoRef.current && duration > 0) {
      const seekTime = (position * duration) / 100;
      await videoRef.current.setPositionAsync(seekTime * 1000);
      dispatch(setCurrentTime(seekTime));
    }
  };

  const handleRewind = async () => {
    hapticFeedback.light();
    if (videoRef.current && currentTime > 10) {
      const newTime = Math.max(0, currentTime - 10);
      await videoRef.current.setPositionAsync(newTime * 1000);
      dispatch(setCurrentTime(newTime));
    }
  };

  const handleFastForward = async () => {
    hapticFeedback.light();
    if (videoRef.current && currentTime < duration - 10) {
      const newTime = Math.min(duration, currentTime + 10);
      await videoRef.current.setPositionAsync(newTime * 1000);
      dispatch(setCurrentTime(newTime));
    }
  };

  const handleFullscreen = async () => {
    hapticFeedback.medium();
    if (videoRef.current) {
      if (isFullscreen) {
        await videoRef.current.dismissFullscreenPlayer();
      } else {
        await videoRef.current.presentFullscreenPlayer();
      }
      dispatch(setFullscreen(!isFullscreen));
    }
  };

  const handlePlaybackSpeedChange = async (speed: number) => {
    hapticFeedback.light();
    setPlaybackSpeed(speed);
    if (videoRef.current) {
      await videoRef.current.setRateAsync(speed, true);
    }
    setShowSpeedSelector(false);
  };

  const handleQualityChange = (newQuality: string) => {
    hapticFeedback.light();
    setQuality(newQuality);
    setShowQualitySelector(false);
    // Quality change implementation would go here
  };

  const toggleSubtitles = () => {
    hapticFeedback.light();
    setSubtitles(!subtitles);
  };

  const handleVolumeChange = async (newVolume: number) => {
    setVolume(newVolume);
    if (videoRef.current) {
      await videoRef.current.setVolumeAsync(newVolume);
    }
  };

  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      dispatch(setCurrentTime(status.positionMillis / 1000));
      dispatch(setDuration(status.durationMillis / 1000));
      dispatch(setPlaying(status.isPlaying));
      dispatch(setBuffering(status.isBuffering));
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    return duration > 0 ? (currentTime / duration) * 100 : 0;
  };

  const checkAccess = () => {
    if (!currentVideo) return false;
    if (!currentVideo.is_premium) return true;
    
    // Check if user has purchased this video or has subscription
    // This would be implemented with actual purchase/subscription logic
    return user?.subscription_status === 'active';
  };

  if (!currentVideo) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading video...</Text>
      </View>
    );
  }

  if (!checkAccess()) {
    return (
      <View style={styles.accessDeniedContainer}>
        <Text style={styles.accessDeniedTitle}>Premium Content</Text>
        <Text style={styles.accessDeniedText}>
          This video requires a premium subscription or purchase to watch.
        </Text>
        <TouchableOpacity style={styles.upgradeButton}>
          <Text style={styles.upgradeButtonText}>Upgrade Now</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      {/* Video Player */}
      <TouchableOpacity
        style={styles.videoContainer}
        activeOpacity={1}
        onPress={() => setShowControls(!showControls)}
      >
        <Video
          ref={videoRef}
          source={{ uri: currentVideo.video_url }}
          style={styles.video}
          useNativeControls={false}
          resizeMode="contain"
          onPlaybackStatusUpdate={onPlaybackStatusUpdate}
          shouldPlay={false}
        />

        {/* Controls Overlay */}
        {showControls && (
          <LinearGradient
            colors={['rgba(0,0,0,0.7)', 'transparent', 'rgba(0,0,0,0.7)']}
            style={styles.controlsOverlay}
          >
            {/* Top Controls */}
            <View style={styles.topControls}>
              <TouchableOpacity style={styles.backButton} onPress={onClose}>
                <Text style={styles.backButtonText}>←</Text>
              </TouchableOpacity>
              <View style={styles.videoTitleContainer}>
                <Text style={styles.videoTitle} numberOfLines={1}>
                  {currentVideo.title}
                </Text>
                <Text style={styles.videoCreator}>
                  by {currentVideo.creator.username}
                </Text>
              </View>
            </View>

            {/* Center Play/Pause */}
            <View style={styles.centerControls}>
              <TouchableOpacity style={styles.playPauseButton} onPress={handlePlayPause}>
                <Text style={styles.playPauseText}>
                  {isPlaying ? '⏸' : '▶'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Bottom Controls */}
            <View style={styles.bottomControls}>
              <View style={styles.progressContainer}>
                <Text style={styles.timeText}>{formatTime(currentTime)}</Text>
                <View style={styles.progressBar}>
                  <View style={styles.progressTrack} />
                  <View
                    style={[
                      styles.progressFill,
                      { width: `${getProgressPercentage()}%` },
                    ]}
                  />
                  <TouchableOpacity
                    style={[
                      styles.progressThumb,
                      { left: `${getProgressPercentage()}%` },
                    ]}
                    onPress={(e) => {
                      const { locationX } = e.nativeEvent;
                      const progressBarWidth = width - 120; // Accounting for time labels
                      const percentage = (locationX / progressBarWidth) * 100;
                      handleSeek(percentage);
                    }}
                  />
                </View>
                <Text style={styles.timeText}>{formatTime(duration)}</Text>
              </View>
              
              <View style={styles.rightControls}>
                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={handleRewind}
                >
                  <Ionicons name="play-back" size={20} color="white" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={handleFastForward}
                >
                  <Ionicons name="play-forward" size={20} color="white" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={() => setShowSpeedSelector(true)}
                >
                  <Text style={styles.speedText}>{playbackSpeed}x</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={toggleSubtitles}
                >
                  <Ionicons
                    name="chatbox"
                    size={20}
                    color={subtitles ? GoGoColors.primary : "white"}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={() => setShowAdvancedControls(true)}
                >
                  <Ionicons name="settings" size={20} color="white" />
                </TouchableOpacity>

                <TouchableOpacity style={styles.fullscreenButton} onPress={handleFullscreen}>
                  <Ionicons name="expand" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        )}
      </TouchableOpacity>

      {/* Video Info */}
      <View style={styles.videoInfo}>
        <Text style={styles.infoTitle}>{currentVideo.title}</Text>
        <Text style={styles.infoDescription}>{currentVideo.description}</Text>
        <View style={styles.videoStats}>
          <Text style={styles.statText}>{currentVideo.view_count} views</Text>
          <Text style={styles.statText}>{currentVideo.like_count} likes</Text>
          <Text style={styles.statText}>{formatTime(currentVideo.duration)}</Text>
        </View>
      </View>

      {/* Speed Selector Modal */}
      <Modal visible={showSpeedSelector} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.speedSelectorModal}>
            <Text style={styles.modalTitle}>Playback Speed</Text>
            <View style={styles.speedButtons}>
              {[0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map(speed => (
                <TouchableOpacity
                  key={speed}
                  style={[
                    styles.speedButton,
                    playbackSpeed === speed && styles.activeSpeedButton
                  ]}
                  onPress={() => handlePlaybackSpeedChange(speed)}
                >
                  <Text style={[
                    styles.speedButtonText,
                    playbackSpeed === speed && styles.activeSpeedButtonText
                  ]}>
                    {speed}x
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowSpeedSelector(false)}
            >
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Advanced Settings Modal */}
      <Modal visible={showAdvancedControls} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={styles.settingsModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Video Settings</Text>
              <TouchableOpacity
                onPress={() => setShowAdvancedControls(false)}
                style={styles.modalHeaderButton}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.settingsContent}>
              {/* Quality Setting */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Quality</Text>
                <TouchableOpacity
                  style={styles.settingValue}
                  onPress={() => setShowQualitySelector(true)}
                >
                  <Text style={styles.settingValueText}>{quality}</Text>
                  <Ionicons name="chevron-forward" size={16} color={GoGoColors.textMuted} />
                </TouchableOpacity>
              </View>

              {/* Volume Setting */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Volume</Text>
                <View style={styles.sliderContainer}>
                  <Slider
                    style={styles.settingSlider}
                    minimumValue={0}
                    maximumValue={1}
                    value={volume}
                    onValueChange={handleVolumeChange}
                    minimumTrackTintColor={GoGoColors.primary}
                    maximumTrackTintColor="rgba(255,255,255,0.3)"
                  />
                  <Text style={styles.sliderValue}>{Math.round(volume * 100)}%</Text>
                </View>
              </View>

              {/* Subtitles Setting */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Subtitles</Text>
                <TouchableOpacity
                  style={styles.settingToggle}
                  onPress={toggleSubtitles}
                >
                  <View style={[
                    styles.toggleSwitch,
                    subtitles && styles.toggleSwitchActive
                  ]}>
                    <View style={[
                      styles.toggleThumb,
                      subtitles && styles.toggleThumbActive
                    ]} />
                  </View>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingText: {
    color: GoGoColors.textPrimary,
    fontSize: 18,
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
    padding: 20,
  },
  accessDeniedTitle: {
    color: GoGoColors.textPrimary,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  accessDeniedText: {
    color: GoGoColors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  upgradeButton: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  upgradeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  closeButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  closeButtonText: {
    color: GoGoColors.textMuted,
    fontSize: 16,
  },
  videoContainer: {
    height: width * 0.56, // 16:9 aspect ratio
    position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  videoTitleContainer: {
    flex: 1,
    marginLeft: 15,
  },
  videoTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  videoCreator: {
    color: '#ccc',
    fontSize: 14,
  },
  centerControls: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  playPauseButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  playPauseText: {
    color: '#fff',
    fontSize: 32,
  },
  bottomControls: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  progressContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    color: '#fff',
    fontSize: 12,
    minWidth: 40,
    textAlign: 'center',
  },
  progressBar: {
    flex: 1,
    height: 20,
    marginHorizontal: 10,
    position: 'relative',
    justifyContent: 'center',
  },
  progressTrack: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
  },
  progressFill: {
    position: 'absolute',
    height: 4,
    backgroundColor: GoGoColors.primary,
    borderRadius: 2,
  },
  progressThumb: {
    position: 'absolute',
    width: 12,
    height: 12,
    backgroundColor: GoGoColors.primary,
    borderRadius: 6,
    marginTop: -4,
    marginLeft: -6,
  },
  rightControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fullscreenButton: {
    padding: 10,
  },
  fullscreenText: {
    color: '#fff',
    fontSize: 20,
  },
  videoInfo: {
    padding: 20,
  },
  infoTitle: {
    color: GoGoColors.textPrimary,
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  infoDescription: {
    color: GoGoColors.textSecondary,
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 15,
  },
  videoStats: {
    flexDirection: 'row',
    gap: 20,
  },
  statText: {
    color: GoGoColors.textMuted,
    fontSize: 12,
  },
  // Enhanced control styles
  speedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  controlButton: {
    padding: 8,
    marginHorizontal: 4,
    borderRadius: 4,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  speedSelectorModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    padding: 20,
    minWidth: 250,
  },
  modalTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  speedButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 10,
    marginBottom: 20,
  },
  speedButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  activeSpeedButton: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  speedButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  activeSpeedButtonText: {
    fontWeight: 'bold',
  },
  modalCloseButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  modalCloseButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  settingsModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    width: '100%',
    position: 'absolute',
    bottom: 0,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  modalHeaderButton: {
    padding: 4,
  },
  settingsContent: {
    padding: 20,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  settingLabel: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  settingValueText: {
    color: GoGoColors.textSecondary,
    fontSize: 14,
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
    maxWidth: 150,
  },
  settingSlider: {
    flex: 1,
    height: 20,
  },
  sliderValue: {
    color: 'white',
    fontSize: 12,
    minWidth: 35,
    textAlign: 'right',
  },
  settingToggle: {
    padding: 4,
  },
  toggleSwitch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: GoGoColors.glassBg,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleSwitchActive: {
    backgroundColor: GoGoColors.primary,
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: 'white',
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
});
