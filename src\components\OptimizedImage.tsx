import React, { useState } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { Image } from 'expo-image';
import { GoGoColors } from '../../constants/Colors';
import { SkeletonLoader } from './SkeletonLoader';

interface OptimizedImageProps {
  source: { uri: string } | number;
  style?: any;
  contentFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  placeholder?: string;
  transition?: number;
  onLoad?: () => void;
  onError?: () => void;
  showLoadingIndicator?: boolean;
  fallbackSource?: { uri: string } | number;
}

export default function OptimizedImage({
  source,
  style,
  contentFit = 'cover',
  placeholder,
  transition = 200,
  onLoad,
  onError,
  showLoadingIndicator = true,
  fallbackSource,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  const getImageSource = () => {
    if (hasError && fallbackSource) {
      return fallbackSource;
    }
    return source;
  };

  return (
    <View style={[styles.container, style]}>
      <Image
        source={getImageSource()}
        style={[styles.image, style]}
        contentFit={contentFit}
        transition={transition}
        placeholder={placeholder}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
      
      {isLoading && showLoadingIndicator && (
        <View style={[styles.loadingContainer, style]}>
          <SkeletonLoader 
            width={style?.width || 100} 
            height={style?.height || 100}
            borderRadius={style?.borderRadius || 0}
          />
        </View>
      )}
      
      {hasError && !fallbackSource && (
        <View style={[styles.errorContainer, style]}>
          <View style={styles.errorContent}>
            <ActivityIndicator size="small" color={GoGoColors.textMuted} />
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundLight,
  },
  errorContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundLight,
  },
  errorContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
