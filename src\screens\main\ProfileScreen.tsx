import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAppDispatch, useAppSelector } from '../../store';
import { signOut } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';

interface ProfileOptionProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress: () => void;
  showArrow?: boolean;
  rightComponent?: React.ReactNode;
}

function ProfileOption({ icon, title, subtitle, onPress, showArrow = true, rightComponent }: ProfileOptionProps) {
  return (
    <TouchableOpacity
      style={styles.option}
      onPress={() => {
        hapticFeedback.light();
        onPress();
      }}
      activeOpacity={0.7}
    >
      <View style={styles.optionLeft}>
        <View style={styles.optionIconContainer}>
          <Ionicons name={icon as any} size={24} color={GoGoColors.primary} />
        </View>
        <View style={styles.optionText}>
          <Text style={styles.optionTitle}>{title}</Text>
          {subtitle && <Text style={styles.optionSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      <View style={styles.optionRight}>
        {rightComponent}
        {showArrow && <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />}
      </View>
    </TouchableOpacity>
  );
}

interface StatItemProps {
  label: string;
  value: string;
  icon: string;
}

function StatItem({ label, value, icon }: StatItemProps) {
  return (
    <View style={styles.statItem}>
      <Ionicons name={icon as any} size={20} color={GoGoColors.primary} />
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  );
}

export default function ProfileScreen() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { watchLater, viewingHistory } = useAppSelector((state) => state.userPreferences);

  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoPlayEnabled, setAutoPlayEnabled] = useState(true);

  // Mock stats - in real app these would come from the store
  const userStats = {
    videosWatched: viewingHistory.length || 127,
    watchTime: '45h 32m',
    myListCount: watchLater.length || 23,
    subscriptionDays: 156,
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => dispatch(signOut()),
        },
      ]
    );
  };

  const handleEditProfile = () => {
    // Navigate to edit profile screen
    Alert.alert('Edit Profile', 'Edit profile functionality would be implemented here');
  };

  const handleSubscription = () => {
    // Navigate to subscription management
    Alert.alert('Subscription', 'Subscription management would be implemented here');
  };

  const handlePaymentMethods = () => {
    // Navigate to payment methods
    Alert.alert('Payment Methods', 'Payment methods management would be implemented here');
  };

  const handleCreatorDashboard = () => {
    // Navigate to creator dashboard
    Alert.alert('Creator Dashboard', 'Creator dashboard would be implemented here');
  };

  const handleSettings = () => {
    // Navigate to settings
    Alert.alert('Settings', 'Settings screen would be implemented here');
  };

  const handleHelp = () => {
    // Navigate to help/support
    Alert.alert('Help & Support', 'Help & support would be implemented here');
  };

  const handleAbout = () => {
    // Show about information
    Alert.alert('About GoGo', 'Version 1.0.0\n\nYour premium streaming experience');
  };

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>User not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Enhanced Profile Header */}
      <LinearGradient
        colors={GoGoColors.premiumGradient as any}
        style={styles.header}
      >
        <View style={styles.profileSection}>
          <View style={styles.profileInfo}>
            <TouchableOpacity style={styles.avatarContainer} onPress={handleEditProfile}>
              {user.avatar_url ? (
                <Image source={{ uri: user.avatar_url }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Text style={styles.avatarText}>
                    {user.full_name.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
              <View style={styles.avatarOverlay}>
                <Ionicons name="camera" size={20} color="white" />
              </View>
            </TouchableOpacity>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>{user.full_name}</Text>
              <Text style={styles.userEmail}>{user.email}</Text>

              {/* Subscription Status */}
              <View style={styles.subscriptionContainer}>
                <View style={styles.subscriptionBadge}>
                  <Ionicons name="diamond" size={16} color="#000000" />
                  <Text style={styles.subscriptionText}>
                    {user.subscription_status || 'Premium Member'}
                  </Text>
                </View>
                <View style={styles.userTypeBadge}>
                  <Text style={styles.userBadgeText}>
                    {user.user_type === 'creator' ? 'Content Creator' : 'Viewer'}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          <TouchableOpacity
            style={styles.editButton}
            onPress={() => {
              hapticFeedback.light();
              handleEditProfile();
            }}
          >
            <Ionicons name="pencil" size={20} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* User Stats */}
        <View style={styles.statsContainer}>
          <StatItem
            label="Videos Watched"
            value={userStats.videosWatched.toString()}
            icon="play-circle"
          />
          <StatItem
            label="Watch Time"
            value={userStats.watchTime}
            icon="time"
          />
          <StatItem
            label="My List"
            value={userStats.myListCount.toString()}
            icon="bookmark"
          />
          <StatItem
            label="Member Days"
            value={userStats.subscriptionDays.toString()}
            icon="calendar"
          />
        </View>
      </LinearGradient>

      {/* Profile Options */}
      <View style={styles.optionsContainer}>
        {/* Account Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          
          <ProfileOption
            icon="person-outline"
            title="Edit Profile"
            subtitle="Update your personal information"
            onPress={handleEditProfile}
          />

          {user.subscription_status && (
            <ProfileOption
              icon="card-outline"
              title="Subscription"
              subtitle={`Status: ${user.subscription_status}`}
              onPress={handleSubscription}
            />
          )}

          <ProfileOption
            icon="wallet-outline"
            title="Payment Methods"
            subtitle="Manage your payment options"
            onPress={handlePaymentMethods}
          />
        </View>

        {/* Creator Section */}
        {user.user_type === 'creator' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Creator Tools</Text>
            
            <ProfileOption
              icon="analytics-outline"
              title="Creator Dashboard"
              subtitle="Manage your content and analytics"
              onPress={handleCreatorDashboard}
            />
          </View>
        )}

        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          
          <ProfileOption
            icon="notifications-outline"
            title="Notifications"
            subtitle="Push notifications for new content"
            onPress={() => {}}
            showArrow={false}
            rightComponent={
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: GoGoColors.glassBg, true: GoGoColors.primary }}
                thumbColor={notificationsEnabled ? '#fff' : GoGoColors.textMuted}
              />
            }
          />

          <ProfileOption
            icon="play-outline"
            title="Auto-Play"
            subtitle="Automatically play next video"
            onPress={() => {}}
            showArrow={false}
            rightComponent={
              <Switch
                value={autoPlayEnabled}
                onValueChange={setAutoPlayEnabled}
                trackColor={{ false: GoGoColors.glassBg, true: GoGoColors.primary }}
                thumbColor={autoPlayEnabled ? '#fff' : GoGoColors.textMuted}
              />
            }
          />

          <ProfileOption
            icon="settings-outline"
            title="Settings"
            subtitle="App preferences and privacy"
            onPress={handleSettings}
          />
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          <ProfileOption
            icon="help-circle-outline"
            title="Help & Support"
            subtitle="Get help and contact support"
            onPress={handleHelp}
          />

          <ProfileOption
            icon="information-circle-outline"
            title="About"
            subtitle="App version and information"
            onPress={handleAbout}
          />
        </View>

        {/* Sign Out */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
            <Ionicons name="log-out-outline" size={24} color={GoGoColors.primary} />
            <Text style={styles.signOutText}>Sign Out</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  errorText: {
    color: GoGoColors.textLight,
    fontSize: 18,
    textAlign: 'center',
    marginTop: 50,
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: 15,
    position: 'relative',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  avatarOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: GoGoColors.primary,
    borderRadius: 12,
    padding: 4,
    borderWidth: 2,
    borderColor: 'white',
  },
  avatarText: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 16,
    marginBottom: 12,
  },
  subscriptionContainer: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  subscriptionBadge: {
    backgroundColor: GoGoColors.premium,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  subscriptionText: {
    color: '#000000',
    fontSize: 12,
    fontWeight: 'bold',
  },
  userTypeBadge: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  userBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  editButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Stats styles
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.2)',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 4,
  },
  statLabel: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
    marginTop: 2,
    textAlign: 'center',
  },
  optionsContainer: {
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    color: GoGoColors.textPrimary,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
    borderRadius: 8,
    marginBottom: 4,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: GoGoColors.glassBg,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    color: GoGoColors.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
  optionSubtitle: {
    color: GoGoColors.textMuted,
    fontSize: 14,
    marginTop: 2,
  },
  optionRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderWidth: 2,
    borderColor: GoGoColors.primary,
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  signOutText: {
    color: GoGoColors.primary,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});
