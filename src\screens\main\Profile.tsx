import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { logout } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';
import SettingsModal from '../../components/SettingsModal';

export default function ProfileScreen() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const [activeTab, setActiveTab] = useState(0);
  const [showSettings, setShowSettings] = useState(false);

  const handleEditProfile = () => {
    Alert.alert('Edit Profile', 'Profile editing feature coming soon!');
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign Out', style: 'destructive', onPress: () => dispatch(logout()) },
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert('About GoGo', 'Version 1.0.0\n\nYour premium streaming experience');
  };

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>User not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Twitter-inspired Header */}
        <View style={styles.header}>
          {/* Cover Photo Area */}
          <View style={styles.coverPhoto}>
            <LinearGradient
              colors={[GoGoColors.primary, GoGoColors.primaryLight]}
              style={styles.coverGradient}
            />
            <TouchableOpacity style={styles.settingsButton} onPress={() => setShowSettings(true)}>
              <Ionicons name="settings-outline" size={24} color={GoGoColors.textLight} />
            </TouchableOpacity>
          </View>

          {/* Profile Info Section */}
          <View style={styles.profileSection}>
            <View style={styles.avatarRow}>
              <TouchableOpacity style={styles.avatarContainer} onPress={handleEditProfile}>
                {user.avatar_url ? (
                  <Image source={{ uri: user.avatar_url }} style={styles.avatar} />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Text style={styles.avatarText}>
                      {user.full_name?.charAt(0).toUpperCase() || 'U'}
                    </Text>
                  </View>
                )}
                <View style={styles.avatarBorder} />
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.editProfileButton} onPress={handleEditProfile}>
                <Text style={styles.editProfileText}>Edit Profile</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.userInfo}>
              <Text style={styles.displayName}>{user.full_name || 'GoGo User'}</Text>
              <Text style={styles.username}>@{user.username}</Text>
              <Text style={styles.bio}>🎬 Content creator • 📱 Mobile enthusiast • ✨ Sharing amazing videos</Text>
              
              {/* Join Date */}
              <View style={styles.joinDate}>
                <Ionicons name="calendar-outline" size={16} color={GoGoColors.textMuted} />
                <Text style={styles.joinDateText}>Joined March 2024</Text>
              </View>

              {/* Stats */}
              <View style={styles.statsContainer}>
                <TouchableOpacity style={styles.statItem}>
                  <Text style={styles.statNumber}>127</Text>
                  <Text style={styles.statLabel}>Following</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.statItem}>
                  <Text style={styles.statNumber}>1,234</Text>
                  <Text style={styles.statLabel}>Followers</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          {['Videos', 'Likes', 'Playlists'].map((tab, index) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === index && styles.activeTab]}
              onPress={() => setActiveTab(index)}
            >
              <Text style={[styles.tabText, activeTab === index && styles.activeTabText]}>
                {tab}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Content Area */}
        <View style={styles.contentArea}>
          {activeTab === 0 && (
            <View style={styles.tabContent}>
              <Text style={styles.tabContentText}>Your Videos</Text>
              <Text style={styles.tabContentSubtext}>Videos you've uploaded will appear here</Text>
            </View>
          )}
          {activeTab === 1 && (
            <View style={styles.tabContent}>
              <Text style={styles.tabContentText}>Liked Videos</Text>
              <Text style={styles.tabContentSubtext}>Videos you've liked will appear here</Text>
            </div>
          )}
          {activeTab === 2 && (
            <View style={styles.tabContent}>
              <Text style={styles.tabContentText}>Your Playlists</Text>
              <Text style={styles.tabContentSubtext}>Your created playlists will appear here</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Settings Modal */}
      <SettingsModal
        visible={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  errorText: {
    color: GoGoColors.error,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 50,
  },
  header: {
    backgroundColor: GoGoColors.backgroundDark,
  },
  coverPhoto: {
    height: 120,
    position: 'relative',
  },
  coverGradient: {
    flex: 1,
  },
  settingsButton: {
    position: 'absolute',
    top: 50,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  avatarRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginTop: -40,
    marginBottom: 12,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: GoGoColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: GoGoColors.textLight,
    fontSize: 32,
    fontWeight: 'bold',
  },
  avatarBorder: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    borderRadius: 44,
    borderWidth: 4,
    borderColor: GoGoColors.backgroundDark,
  },
  editProfileButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: GoGoColors.border,
    backgroundColor: GoGoColors.backgroundDark,
  },
  editProfileText: {
    color: GoGoColors.textPrimary,
    fontSize: 14,
    fontWeight: '600',
  },
  userInfo: {
    gap: 8,
  },
  displayName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  username: {
    fontSize: 15,
    color: GoGoColors.textMuted,
  },
  bio: {
    fontSize: 15,
    color: GoGoColors.textPrimary,
    lineHeight: 20,
    marginTop: 4,
  },
  joinDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 4,
  },
  joinDateText: {
    fontSize: 14,
    color: GoGoColors.textMuted,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 20,
    marginTop: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  statNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  statLabel: {
    fontSize: 14,
    color: GoGoColors.textMuted,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
    backgroundColor: GoGoColors.backgroundDark,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: GoGoColors.primary,
  },
  tabText: {
    fontSize: 15,
    fontWeight: '500',
    color: GoGoColors.textMuted,
  },
  activeTabText: {
    color: GoGoColors.textPrimary,
    fontWeight: '600',
  },
  contentArea: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  tabContent: {
    padding: 40,
    alignItems: 'center',
  },
  tabContentText: {
    fontSize: 18,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  tabContentSubtext: {
    fontSize: 14,
    color: GoGoColors.textMuted,
    textAlign: 'center',
  },
});
