import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { GoGoColors } from '../../constants/Colors';
import { useResponsiveLayout } from '../utils/responsive';

const { width } = Dimensions.get('window');

interface SkeletonLoaderProps {
  width?: number;
  height?: number;
  borderRadius?: number;
  style?: any;
}

export function SkeletonLoader({ 
  width: skeletonWidth = 100, 
  height = 20, 
  borderRadius = 4,
  style 
}: SkeletonLoaderProps) {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1000 }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      shimmerValue.value,
      [0, 1],
      [-skeletonWidth, skeletonWidth],
      Extrapolate.CLAMP
    );

    return {
      transform: [{ translateX }],
    };
  });

  return (
    <View 
      style={[
        styles.skeleton, 
        { width: skeletonWidth, height, borderRadius },
        style
      ]}
    >
      <Animated.View style={[styles.shimmer, animatedStyle]} />
    </View>
  );
}

interface VideoCardSkeletonProps {
  size?: 'small' | 'medium' | 'large';
}

export function VideoCardSkeleton({ size = 'medium' }: VideoCardSkeletonProps) {
  const layout = useResponsiveLayout();
  
  const cardWidth = size === 'large' 
    ? layout.width * 0.8 
    : size === 'medium' 
      ? layout.cardWidth 
      : layout.width * 0.3;
  const cardHeight = cardWidth * (size === 'large' ? 0.6 : 1.5);

  return (
    <View style={[styles.videoCardSkeleton, { width: cardWidth }]}>
      <SkeletonLoader 
        width={cardWidth} 
        height={cardHeight * 0.7} 
        borderRadius={8}
        style={styles.thumbnailSkeleton}
      />
      <View style={styles.videoCardContent}>
        <SkeletonLoader width={cardWidth * 0.9} height={16} />
        <SkeletonLoader width={cardWidth * 0.6} height={12} style={{ marginTop: 8 }} />
        <SkeletonLoader width={cardWidth * 0.4} height={12} style={{ marginTop: 4 }} />
      </View>
    </View>
  );
}

interface VideoRowSkeletonProps {
  title?: string;
  itemCount?: number;
  size?: 'small' | 'medium' | 'large';
}

export function VideoRowSkeleton({ title, itemCount = 3, size = 'medium' }: VideoRowSkeletonProps) {
  return (
    <View style={styles.videoRowSkeleton}>
      {title && (
        <SkeletonLoader width={150} height={24} style={styles.rowTitleSkeleton} />
      )}
      <View style={styles.videoRowContent}>
        {Array.from({ length: itemCount }).map((_, index) => (
          <VideoCardSkeleton key={index} size={size} />
        ))}
      </View>
    </View>
  );
}

interface FeaturedVideoSkeletonProps {}

export function FeaturedVideoSkeleton({}: FeaturedVideoSkeletonProps) {
  return (
    <View style={styles.featuredSkeleton}>
      <SkeletonLoader 
        width={width} 
        height={width * 1.2} 
        borderRadius={0}
      />
      <View style={styles.featuredContent}>
        <SkeletonLoader width={width * 0.7} height={32} />
        <SkeletonLoader width={width * 0.9} height={16} style={{ marginTop: 12 }} />
        <SkeletonLoader width={width * 0.8} height={16} style={{ marginTop: 8 }} />
        <View style={styles.featuredActions}>
          <SkeletonLoader width={120} height={48} borderRadius={24} />
          <SkeletonLoader width={100} height={48} borderRadius={24} style={{ marginLeft: 12 }} />
        </View>
      </View>
    </View>
  );
}

interface ProfileSkeletonProps {}

export function ProfileSkeleton({}: ProfileSkeletonProps) {
  return (
    <View style={styles.profileSkeleton}>
      <View style={styles.profileHeader}>
        <SkeletonLoader width={80} height={80} borderRadius={40} />
        <View style={styles.profileInfo}>
          <SkeletonLoader width={200} height={24} />
          <SkeletonLoader width={150} height={16} style={{ marginTop: 8 }} />
          <SkeletonLoader width={120} height={20} style={{ marginTop: 8 }} />
        </View>
      </View>
      <View style={styles.profileStats}>
        {Array.from({ length: 4 }).map((_, index) => (
          <View key={index} style={styles.statSkeleton}>
            <SkeletonLoader width={40} height={40} borderRadius={20} />
            <SkeletonLoader width={60} height={16} style={{ marginTop: 8 }} />
            <SkeletonLoader width={80} height={12} style={{ marginTop: 4 }} />
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: GoGoColors.backgroundLight,
    overflow: 'hidden',
  },
  shimmer: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  // Video Card Skeleton
  videoCardSkeleton: {
    marginRight: 12,
    marginBottom: 16,
  },
  thumbnailSkeleton: {
    marginBottom: 8,
  },
  videoCardContent: {
    paddingHorizontal: 4,
  },
  // Video Row Skeleton
  videoRowSkeleton: {
    marginBottom: 24,
  },
  rowTitleSkeleton: {
    marginLeft: 16,
    marginBottom: 16,
  },
  videoRowContent: {
    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  // Featured Video Skeleton
  featuredSkeleton: {
    position: 'relative',
    marginBottom: 24,
  },
  featuredContent: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
  },
  featuredActions: {
    flexDirection: 'row',
    marginTop: 20,
  },
  // Profile Skeleton
  profileSkeleton: {
    padding: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  profileInfo: {
    marginLeft: 16,
    flex: 1,
  },
  profileStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statSkeleton: {
    alignItems: 'center',
  },
});
