import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import HomeScreen from '../screens/main/HomeScreen';
import SearchScreen from '../screens/main/SearchScreen';
import LibraryScreen from '../screens/LibraryScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import { Video } from '../types';
import { GoGoColors } from '../../constants/Colors';

const Tab = createBottomTabNavigator();

interface Props {
  onVideoPress: (video: Video) => void;
}

export default function MainTabNavigator({ onVideoPress }: Props) {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Search') {
            iconName = focused ? 'search' : 'search-outline';
          } else if (route.name === 'Library') {
            iconName = focused ? 'library' : 'library-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'home-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: GoGoColors.highlightGold,
        tabBarInactiveTintColor: GoGoColors.textMuted,
        tabBarStyle: {
          backgroundColor: GoGoColors.backgroundDark,
          borderTopColor: GoGoColors.border,
          borderTopWidth: 1,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        headerStyle: {
          backgroundColor: GoGoColors.backgroundDark,
          borderBottomColor: GoGoColors.border,
          borderBottomWidth: 1,
        },
        headerTintColor: GoGoColors.textLight,
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 20,
        },
      })}
    >
      <Tab.Screen 
        name="Home" 
        options={{
          title: 'GoGo',
          headerStyle: {
            backgroundColor: GoGoColors.backgroundDark,
            borderBottomWidth: 0,
          },
        }}
      >
        {() => <HomeScreen onVideoPress={onVideoPress} />}
      </Tab.Screen>
      
      <Tab.Screen 
        name="Search" 
        options={{ title: 'Search' }}
      >
        {() => <SearchScreen onVideoPress={onVideoPress} />}
      </Tab.Screen>
      
      <Tab.Screen 
        name="Library" 
        options={{ 
          title: 'My Library',
          headerStyle: {
            backgroundColor: GoGoColors.backgroundDark,
            borderBottomColor: GoGoColors.border,
            borderBottomWidth: 1,
          },
        }}
      >
        {() => <LibraryScreen />}
      </Tab.Screen>
      
      <Tab.Screen 
        name="Profile" 
        options={{ title: 'Profile' }}
        component={ProfileScreen}
      />
    </Tab.Navigator>
  );
}
