import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { StreamFlixColors } from '../../constants/Colors';
import { hapticFeedback } from '../utils/animations';

interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
}

interface SettingItemProps {
  title: string;
  subtitle?: string;
  icon: string;
  type: 'toggle' | 'picker' | 'button';
  value?: any;
  onValueChange?: (value: any) => void;
  options?: { label: string; value: any }[];
  onPress?: () => void;
}

function SettingItem({ 
  title, 
  subtitle, 
  icon, 
  type, 
  value, 
  onValueChange, 
  options, 
  onPress 
}: SettingItemProps) {
  const [showPicker, setShowPicker] = useState(false);

  const renderControl = () => {
    switch (type) {
      case 'toggle':
        return (
          <Switch
            value={value}
            onValueChange={(newValue) => {
              hapticFeedback.light();
              onValueChange?.(newValue);
            }}
            trackColor={{ false: StreamFlixColors.glassBg, true: StreamFlixColors.primary }}
            thumbColor={value ? '#FFFFFF' : StreamFlixColors.textMuted}
          />
        );
      
      case 'picker':
        return (
          <TouchableOpacity 
            style={styles.pickerButton}
            onPress={() => {
              hapticFeedback.light();
              setShowPicker(true);
            }}
          >
            <Text style={styles.pickerButtonText}>
              {options?.find(opt => opt.value === value)?.label || 'Select'}
            </Text>
            <Ionicons name="chevron-down" size={16} color={StreamFlixColors.textMuted} />
          </TouchableOpacity>
        );
      
      case 'button':
        return (
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => {
              hapticFeedback.light();
              onPress?.();
            }}
          >
            <Ionicons name="chevron-forward" size={16} color={StreamFlixColors.textMuted} />
          </TouchableOpacity>
        );
      
      default:
        return null;
    }
  };

  return (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIconContainer}>
          <Ionicons name={icon as any} size={20} color={StreamFlixColors.primary} />
        </View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      
      <View style={styles.settingRight}>
        {renderControl()}
      </View>

      {/* Picker Modal */}
      {type === 'picker' && (
        <Modal visible={showPicker} transparent animationType="slide">
          <View style={styles.pickerModalOverlay}>
            <View style={styles.pickerModal}>
              <View style={styles.pickerHeader}>
                <Text style={styles.pickerTitle}>{title}</Text>
                <TouchableOpacity onPress={() => setShowPicker(false)}>
                  <Ionicons name="close" size={24} color="white" />
                </TouchableOpacity>
              </View>
              <Picker
                selectedValue={value}
                onValueChange={(itemValue) => {
                  onValueChange?.(itemValue);
                  setShowPicker(false);
                }}
                style={styles.picker}
              >
                {options?.map((option) => (
                  <Picker.Item 
                    key={option.value} 
                    label={option.label} 
                    value={option.value}
                    color="white"
                  />
                ))}
              </Picker>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

export default function SettingsModal({ visible, onClose }: SettingsModalProps) {
  const [settings, setSettings] = useState({
    notifications: true,
    autoPlay: true,
    downloadQuality: 'high',
    streamingQuality: 'auto',
    darkMode: true,
    language: 'en',
    subtitles: false,
    parentalControls: false,
    dataUsage: 'normal',
  });

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached data. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear', 
          style: 'destructive',
          onPress: () => {
            // Clear cache logic here
            Alert.alert('Success', 'Cache cleared successfully');
          }
        },
      ]
    );
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'This will reset all settings to default. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: () => {
            setSettings({
              notifications: true,
              autoPlay: true,
              downloadQuality: 'high',
              streamingQuality: 'auto',
              darkMode: true,
              language: 'en',
              subtitles: false,
              parentalControls: false,
              dataUsage: 'normal',
            });
          }
        },
      ]
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Settings</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="white" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Playback Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Playback</Text>
            
            <SettingItem
              title="Auto-Play"
              subtitle="Automatically play next video"
              icon="play"
              type="toggle"
              value={settings.autoPlay}
              onValueChange={(value) => updateSetting('autoPlay', value)}
            />
            
            <SettingItem
              title="Streaming Quality"
              subtitle="Video quality for streaming"
              icon="videocam"
              type="picker"
              value={settings.streamingQuality}
              onValueChange={(value) => updateSetting('streamingQuality', value)}
              options={[
                { label: 'Auto', value: 'auto' },
                { label: 'High (1080p)', value: 'high' },
                { label: 'Medium (720p)', value: 'medium' },
                { label: 'Low (480p)', value: 'low' },
              ]}
            />
            
            <SettingItem
              title="Subtitles"
              subtitle="Show subtitles by default"
              icon="chatbox"
              type="toggle"
              value={settings.subtitles}
              onValueChange={(value) => updateSetting('subtitles', value)}
            />
          </View>

          {/* Download Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Downloads</Text>
            
            <SettingItem
              title="Download Quality"
              subtitle="Quality for offline downloads"
              icon="download"
              type="picker"
              value={settings.downloadQuality}
              onValueChange={(value) => updateSetting('downloadQuality', value)}
              options={[
                { label: 'High (1080p)', value: 'high' },
                { label: 'Medium (720p)', value: 'medium' },
                { label: 'Low (480p)', value: 'low' },
              ]}
            />
            
            <SettingItem
              title="Data Usage"
              subtitle="Control data consumption"
              icon="cellular"
              type="picker"
              value={settings.dataUsage}
              onValueChange={(value) => updateSetting('dataUsage', value)}
              options={[
                { label: 'High', value: 'high' },
                { label: 'Normal', value: 'normal' },
                { label: 'Low', value: 'low' },
              ]}
            />
          </View>

          {/* Notifications */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notifications</Text>
            
            <SettingItem
              title="Push Notifications"
              subtitle="Get notified about new content"
              icon="notifications"
              type="toggle"
              value={settings.notifications}
              onValueChange={(value) => updateSetting('notifications', value)}
            />
          </View>

          {/* Privacy & Security */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Privacy & Security</Text>
            
            <SettingItem
              title="Parental Controls"
              subtitle="Restrict content access"
              icon="shield"
              type="toggle"
              value={settings.parentalControls}
              onValueChange={(value) => updateSetting('parentalControls', value)}
            />
          </View>

          {/* App Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>App</Text>
            
            <SettingItem
              title="Language"
              subtitle="App language"
              icon="language"
              type="picker"
              value={settings.language}
              onValueChange={(value) => updateSetting('language', value)}
              options={[
                { label: 'English', value: 'en' },
                { label: 'Spanish', value: 'es' },
                { label: 'French', value: 'fr' },
                { label: 'German', value: 'de' },
              ]}
            />
            
            <SettingItem
              title="Clear Cache"
              subtitle="Free up storage space"
              icon="trash"
              type="button"
              onPress={handleClearCache}
            />
            
            <SettingItem
              title="Reset Settings"
              subtitle="Reset all settings to default"
              icon="refresh"
              type="button"
              onPress={handleResetSettings}
            />
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: StreamFlixColors.backgroundDark,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
  },
  headerTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: StreamFlixColors.glassBg,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  settingSubtitle: {
    color: StreamFlixColors.textMuted,
    fontSize: 14,
    marginTop: 2,
  },
  settingRight: {
    marginLeft: 16,
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: StreamFlixColors.glassBg,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  pickerButtonText: {
    color: 'white',
    fontSize: 14,
  },
  actionButton: {
    padding: 8,
  },
  // Picker Modal
  pickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'flex-end',
  },
  pickerModal: {
    backgroundColor: StreamFlixColors.backgroundCard,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '50%',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
  },
  pickerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  picker: {
    backgroundColor: StreamFlixColors.backgroundCard,
    color: 'white',
  },
});
