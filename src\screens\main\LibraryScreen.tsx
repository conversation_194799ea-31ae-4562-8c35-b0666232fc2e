import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../../store';
import { Video } from '../../types';
import { GoGoColors } from '../../../constants/Colors';

interface LibraryVideoProps {
  video: Video;
  onPress: (video: Video) => void;
  onRemove: (video: Video) => void;
}

function LibraryVideo({ video, onPress, onRemove }: LibraryVideoProps) {
  return (
    <TouchableOpacity style={styles.videoItem} onPress={() => onPress(video)}>
      <Image source={{ uri: video.thumbnail_url }} style={styles.videoThumbnail} />
      <View style={styles.videoInfo}>
        <Text style={styles.videoTitle} numberOfLines={2}>
          {video.title}
        </Text>
        <Text style={styles.videoCreator}>{video.creator.username}</Text>
        <View style={styles.videoStats}>
          <Text style={styles.videoStat}>{video.view_count} views</Text>
          <Text style={styles.videoStat}>•</Text>
          <Text style={styles.videoStat}>{Math.floor(video.duration / 60)}m</Text>
        </View>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => onRemove(video)}
      >
        <Ionicons name="ellipsis-vertical" size={20} color="#ccc" />
      </TouchableOpacity>
    </TouchableOpacity>
  );
}

interface Props {
  onVideoPress: (video: Video) => void;
}

export default function LibraryScreen({ onVideoPress }: Props) {
  const { user } = useAppSelector((state) => state.auth);
  
  // Mock data - in real app, this would come from API
  const [watchHistory, setWatchHistory] = useState<Video[]>([]);
  const [favorites, setFavorites] = useState<Video[]>([]);
  const [downloads, setDownloads] = useState<Video[]>([]);
  const [activeTab, setActiveTab] = useState<'history' | 'favorites' | 'downloads'>('history');

  useEffect(() => {
    // Load user's library data
    loadLibraryData();
  }, [user]);

  const loadLibraryData = async () => {
    // This would fetch from your backend
    // For now, using mock data
    setWatchHistory([]);
    setFavorites([]);
    setDownloads([]);
  };

  const handleRemoveFromLibrary = (video: Video) => {
    Alert.alert(
      'Remove from Library',
      `Remove "${video.title}" from your ${activeTab}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            switch (activeTab) {
              case 'history':
                setWatchHistory(prev => prev.filter(v => v.id !== video.id));
                break;
              case 'favorites':
                setFavorites(prev => prev.filter(v => v.id !== video.id));
                break;
              case 'downloads':
                setDownloads(prev => prev.filter(v => v.id !== video.id));
                break;
            }
          },
        },
      ]
    );
  };

  const getCurrentData = () => {
    switch (activeTab) {
      case 'history':
        return watchHistory;
      case 'favorites':
        return favorites;
      case 'downloads':
        return downloads;
      default:
        return [];
    }
  };

  const getEmptyMessage = () => {
    switch (activeTab) {
      case 'history':
        return {
          icon: 'time-outline',
          title: 'No Watch History',
          message: 'Videos you watch will appear here',
        };
      case 'favorites':
        return {
          icon: 'heart-outline',
          title: 'No Favorites',
          message: 'Videos you like will appear here',
        };
      case 'downloads':
        return {
          icon: 'download-outline',
          title: 'No Downloads',
          message: 'Downloaded videos will appear here',
        };
      default:
        return {
          icon: 'library-outline',
          title: 'Empty Library',
          message: 'Your content will appear here',
        };
    }
  };

  const renderVideo = ({ item }: { item: Video }) => (
    <LibraryVideo
      video={item}
      onPress={onVideoPress}
      onRemove={handleRemoveFromLibrary}
    />
  );

  const currentData = getCurrentData();
  const emptyMessage = getEmptyMessage();

  return (
    <View style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => setActiveTab('history')}
        >
          <Ionicons
            name="time-outline"
            size={20}
            color={activeTab === 'history' ? StreamFlixColors.highlightGold : StreamFlixColors.textSecondary}
          />
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
            History
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'favorites' && styles.activeTab]}
          onPress={() => setActiveTab('favorites')}
        >
          <Ionicons
            name="heart-outline"
            size={20}
            color={activeTab === 'favorites' ? StreamFlixColors.highlightGold : StreamFlixColors.textSecondary}
          />
          <Text style={[styles.tabText, activeTab === 'favorites' && styles.activeTabText]}>
            Favorites
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'downloads' && styles.activeTab]}
          onPress={() => setActiveTab('downloads')}
        >
          <Ionicons
            name="download-outline"
            size={20}
            color={activeTab === 'downloads' ? StreamFlixColors.highlightGold : StreamFlixColors.textSecondary}
          />
          <Text style={[styles.tabText, activeTab === 'downloads' && styles.activeTabText]}>
            Downloads
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {currentData.length > 0 ? (
          <FlatList
            data={currentData}
            keyExtractor={(item) => item.id}
            renderItem={renderVideo}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.videoList}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name={emptyMessage.icon as any} size={80} color="#333" />
            <Text style={styles.emptyTitle}>{emptyMessage.title}</Text>
            <Text style={styles.emptyText}>{emptyMessage.message}</Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: StreamFlixColors.backgroundDark,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: StreamFlixColors.border,
    backgroundColor: StreamFlixColors.backgroundMedium,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    gap: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: StreamFlixColors.highlightGold,
  },
  tabText: {
    color: StreamFlixColors.textSecondary,
    fontSize: 14,
    fontWeight: '500',
  },
  activeTabText: {
    color: StreamFlixColors.highlightGold,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  videoList: {
    paddingVertical: 10,
  },
  videoItem: {
    flexDirection: 'row',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
    alignItems: 'center',
  },
  videoThumbnail: {
    width: 120,
    height: 68,
    borderRadius: 8,
    backgroundColor: '#333',
  },
  videoInfo: {
    flex: 1,
    marginLeft: 15,
  },
  videoTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  videoCreator: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 6,
  },
  videoStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  videoStat: {
    color: '#999',
    fontSize: 12,
    marginRight: 8,
  },
  removeButton: {
    padding: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  emptyText: {
    color: '#ccc',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});
