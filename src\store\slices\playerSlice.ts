import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PlayerState } from '../../types';

const initialState: PlayerState = {
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 1.0,
  isFullscreen: false,
  quality: '720p',
  isBuffering: false,
};

const playerSlice = createSlice({
  name: 'player',
  initialState,
  reducers: {
    setPlaying: (state, action: PayloadAction<boolean>) => {
      state.isPlaying = action.payload;
    },
    setCurrentTime: (state, action: PayloadAction<number>) => {
      state.currentTime = action.payload;
    },
    setDuration: (state, action: PayloadAction<number>) => {
      state.duration = action.payload;
    },
    setVolume: (state, action: PayloadAction<number>) => {
      state.volume = Math.max(0, Math.min(1, action.payload));
    },
    setFullscreen: (state, action: PayloadAction<boolean>) => {
      state.isFullscreen = action.payload;
    },
    setQuality: (state, action: PayloadAction<string>) => {
      state.quality = action.payload;
    },
    setBuffering: (state, action: PayloadAction<boolean>) => {
      state.isBuffering = action.payload;
    },
    seekTo: (state, action: PayloadAction<number>) => {
      state.currentTime = action.payload;
    },
    resetPlayer: (state) => {
      return initialState;
    },
  },
});

export const {
  setPlaying,
  setCurrentTime,
  setDuration,
  setVolume,
  setFullscreen,
  setQuality,
  setBuffering,
  seekTo,
  resetPlayer,
} = playerSlice.actions;

export default playerSlice.reducer;
