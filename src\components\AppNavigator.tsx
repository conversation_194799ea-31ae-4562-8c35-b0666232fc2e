import React, { useEffect, useState } from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { useAppDispatch, useAppSelector } from '../store';
import { getCurrentUser } from '../store/slices/authSlice';
import { GoGoColors } from '../../constants/Colors';
import AuthScreen from '../screens/auth/AuthScreen';
import MainTabNavigator from './MainTabNavigator';
import VideoPlayerScreen from '../screens/main/VideoPlayerScreen';
import { Video } from '../types';

export default function AppNavigator() {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);
  
  const [currentVideoId, setCurrentVideoId] = useState<string | null>(null);

  useEffect(() => {
    // Check for existing session on app start
    dispatch(getCurrentUser());
  }, [dispatch]);

  const handleVideoPress = (video: Video) => {
    setCurrentVideoId(video.id);
  };

  const handleCloseVideo = () => {
    setCurrentVideoId(null);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        {/* You can add a loading spinner here */}
      </View>
    );
  }

  if (currentVideoId) {
    return (
      <VideoPlayerScreen
        videoId={currentVideoId}
        onClose={handleCloseVideo}
      />
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {isAuthenticated ? (
        <MainTabNavigator onVideoPress={handleVideoPress} />
      ) : (
        <AuthScreen />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
